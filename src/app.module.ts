import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import config, { AdminDB } from './config';
import { ApplicantsModule } from './modules/applicants/applicants.module';
import { AuthModule } from './modules/auth/auth.module';
import { BrandModule } from './modules/brand/brand.module';
import { CampsModule } from './modules/camp/camp.module';
import { DomainsModule } from './modules/domains/domains.module';
import { EmailTemplatesModule } from './modules/email-templates/email-templates.module';
import { FileShortLinksModule } from './modules/file-short-links/file-short-links.module';
import { JobsModule } from './modules/jobs/jobs.module';
import { PagesModule } from './modules/pages/page.module';
import { TelegramModule } from './modules/telegram/telegram.module';
import { UsersModule } from './modules/users/users.module';
import { CustomThrottlerGuard } from './shared/guards/custom-throttler.guard';
const NODE_ENV = process.env.NODE_ENV;

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: config,
      isGlobal: true,
      envFilePath: [`.env.${NODE_ENV}`, '.env'],
    }),

    /**
     * allows 100 requests per minute (60 seconds),
     */
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 100,
        },
      ],
    }),

    // Database modules
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const config = configService.get(AdminDB);
        if (!config) {
          throw new Error('Cannot start app without ORM config');
        }
        return {
          uri: `${config.url}/${config.database}${
            config.replicaSet ? `?replicaSet=${config.replicaSet}` : ''
          }`,
        };
      },
      connectionName: AdminDB,
      inject: [ConfigService],
    }),

    // Config module
    ConfigModule.forRoot({
      isGlobal: true, // Makes the configuration available globally
    }),

    // Schedule module for cron jobs
    ScheduleModule.forRoot(),

    // Modules
    TelegramModule,
    AuthModule,
    JobsModule,
    ApplicantsModule,
    PagesModule,
    DomainsModule,
    // FilesModule,
    // ShortLinkModule,
    UsersModule,
    FileShortLinksModule,
    BrandModule,
    CampsModule,
    // JobPostingModule,
    // WebhookModule,
    EmailTemplatesModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
  ],
})
export class AppModule {}
