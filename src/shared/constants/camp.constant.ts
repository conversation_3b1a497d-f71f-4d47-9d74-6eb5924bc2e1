// Removed duplicate CAMP_STATUS enum - using the one below

export enum EMAIL_STATUS {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  FAILED = 'failed',
  UNSUBSCRIBED = 'unsubscribed',
}

export enum CAMP_USER_STATUS {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  UNSUBSCRIBED = 'unsubscribed',
}

export enum CAMP_STATUS {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ARCHIVED = 'archived',
}

export enum EMAIL_TYPE {
  START = 'start',
  END = 'end',
  OTHER = 'other',
}

// Helper function để tạo dynamic other email types
export const createOtherEmailType = (index: number): string => {
  return `other_${index}`;
};

// Helper function để validate email type
export const isValidEmailType = (emailType: string): boolean => {
  if (emailType === EMAIL_TYPE.START || emailType === EMAIL_TYPE.END) {
    return true;
  }
  return /^other_\d+$/.test(emailType);
};

// Email Campaign Stages
export enum EMAIL_CAMPAIGN_STAGE {
  INITIATION = 'initiation', // Giai đoạn 1: Bắt buộc gửi
  ENGAGEMENT = 'engagement', // Giai đoạn 2: Kích thích mở email
  CONVERSION = 'conversion', // Giai đoạn 3: Thúc đẩy hành động
  RETENTION = 'retention', // Giai đoạn 4: Duy trì quan hệ
}

// Email Campaign Stage Status
export enum STAGE_STATUS {
  PENDING = 'pending', // Chưa bắt đầu
  IN_PROGRESS = 'in_progress', // Đang thực hiện
  COMPLETED = 'completed', // Hoàn thành
  SKIPPED = 'skipped', // Bỏ qua (do điều kiện)
  STOPPED = 'stopped', // Dừng (do không mở email)
}

// Email Campaign User Status
export enum USER_CAMPAIGN_STATUS {
  ACTIVE = 'active', // Đang tham gia campaign
  COMPLETED = 'completed', // Hoàn thành campaign
  STOPPED = 'stopped', // Dừng campaign (do không tương tác)
  PAUSED = 'paused', // Tạm dừng
}

export enum PERIODIC_FREQUENCY {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}
