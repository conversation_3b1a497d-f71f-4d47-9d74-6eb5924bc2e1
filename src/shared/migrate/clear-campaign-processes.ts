import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { AdminDB } from '../../config';
import { CampaignStageProgress } from '../entities/camp/campaign-stage-progress.entity';
import { Camp } from '../entities/camp/camp.entity';
import { CAMP_STATUS } from '../constants/camp.constant';

/**
 * Migration script to clear all campaign processes
 * Usage: npm run migrate:clear-processes
 */
async function clearCampaignProcesses() {
  console.log('🚀 Starting campaign processes cleanup...');

  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    // Get models
    const campaignStageProgressModel = app.get<Model<any>>(
      getModelToken(CampaignStageProgress.name, AdminDB)
    );
    const campModel = app.get<Model<any>>(
      getModelToken(Camp.name, AdminDB)
    );

    console.log('📊 Checking current state...');

    // Count current records
    const progressCount = await campaignStageProgressModel.countDocuments({});
    const activeCampsCount = await campModel.countDocuments({ 
      status: CAMP_STATUS.ACTIVE 
    });

    console.log(`📈 Found ${progressCount} campaign stage progresses`);
    console.log(`📈 Found ${activeCampsCount} active campaigns`);

    // Clear all campaign stage progresses
    console.log('🧹 Clearing campaign stage progresses...');
    const progressResult = await campaignStageProgressModel.deleteMany({});
    console.log(`✅ Cleared ${progressResult.deletedCount} campaign stage progresses`);

    // Set all active campaigns to paused
    console.log('⏸️ Pausing all active campaigns...');
    const campResult = await campModel.updateMany(
      { status: CAMP_STATUS.ACTIVE },
      { 
        status: CAMP_STATUS.PAUSED,
        updatedAt: new Date(),
      }
    );
    console.log(`✅ Paused ${campResult.modifiedCount} campaigns`);

    // Final count
    const finalProgressCount = await campaignStageProgressModel.countDocuments({});
    const finalActiveCampsCount = await campModel.countDocuments({ 
      status: CAMP_STATUS.ACTIVE 
    });

    console.log('📊 Final state:');
    console.log(`📈 Campaign stage progresses: ${finalProgressCount}`);
    console.log(`📈 Active campaigns: ${finalActiveCampsCount}`);

    console.log('🎉 Campaign processes cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the migration
if (require.main === module) {
  clearCampaignProcesses()
    .then(() => {
      console.log('✅ Migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

export { clearCampaignProcesses };
