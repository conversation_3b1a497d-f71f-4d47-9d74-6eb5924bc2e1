import { Injectable, Logger } from '@nestjs/common';
import { TinyUrlService } from './tiny-url.service';
import { FileShortLinksService } from 'src/modules/file-short-links/file-short-links.service';
import { DubLinkService } from './dub';

export interface DownloadUrlResult {
  success: boolean;
  originalUrl?: string;
  dubUrl?: string;
  tinyUrl?: string;
  shortLinkId?: string;
  error?: string;
}

/**
 * Build Dub link configuration
 */
export const buildDubLinkConfig = (
  url: string,
  shortLinkId: string,
  expirationDays: number = 3,
) => ({
  url: `https://${url}`,
  externalId: shortLinkId,
  expiresAt: new Date(
    Date.now() + expirationDays * 24 * 60 * 60 * 1000,
  ).toISOString(),
  utm_campaign: shortLinkId,
  domain: 'dub.sh',
});

@Injectable()
export class EmailContentProcessorService {
  private readonly logger = new Logger(EmailContentProcessorService.name);

  constructor(
    private readonly tinyUrlService: TinyUrlService,
    private readonly fileShortLinksService: FileShortLinksService,
    private readonly dubLinkService: DubLinkService,
  ) {}

  /**
   * Process email content and replace DOWNLOAD_URL placeholder with tiny URL
   */
  async processEmailContent(
    emailContent: string,
    emailTemplate: any,
  ): Promise<{ content: string; downloadUrlResult?: DownloadUrlResult }> {
    try {
      // Check if DOWNLOAD_URL is in requirements
      if (!emailTemplate.requirements?.includes('DOWNLOAD_URL')) {
        return { content: emailContent };
      }

      // Check if content contains DOWNLOAD_URL placeholder
      if (!emailContent.includes('{{DOWNLOAD_URL}}')) {
        return { content: emailContent };
      }

      // Create download URL
      const downloadUrlResult = await this.createDownloadUrl(emailTemplate);

      if (!downloadUrlResult.success) {
        return {
          content: emailContent.replace(
            /\{\{DOWNLOAD_URL\}\}/g,
            downloadUrlResult.tinyUrl || '#',
          ),
          downloadUrlResult,
        };
      }

      // Replace placeholder with actual tiny URL
      const processedContent = emailContent.replace(
        /\{\{DOWNLOAD_URL\}\}/g,
        downloadUrlResult.tinyUrl || downloadUrlResult.originalUrl || '#',
      );

      return {
        content: processedContent,
        downloadUrlResult,
      };
    } catch (error) {
      this.logger.error('Failed to process email content:', error);
      return {
        content: emailContent.replace(/\{\{DOWNLOAD_URL\}\}/g, '#'),
        downloadUrlResult: {
          success: false,
          error: `Failed to process email content: ${error.message}`,
        },
      };
    }
  }

  /**
   * Create download URL with UTM campaign and tiny URL
   */
  private async createDownloadUrl(
    emailTemplate: any,
  ): Promise<DownloadUrlResult> {
    try {
      this.logger.log(
        `🔗 Starting download URL creation for template: ${emailTemplate.name || 'unknown'}`,
      );
      this.logger.log(
        `📋 Template data: domain=${emailTemplate.domain}, file=${emailTemplate.file}, prefix=${emailTemplate.prefix}`,
      );

      // Create file short link first
      this.logger.log(`📎 Step 1: Creating file short link...`);
      const shortLinkResult = await this.createFileShortLink(emailTemplate);

      if (!shortLinkResult.success) {
        this.logger.error(`❌ Step 1 FAILED: ${shortLinkResult.error}`);
        return {
          success: false,
          error: `Failed to create short link: ${shortLinkResult.error}`,
        };
      }

      this.logger.log(
        `✅ Step 1 SUCCESS: shortLinkId=${shortLinkResult.shortLinkId}`,
      );

      // Step 2: Build Dub config and get original URL from it
      const baseUrl = `${emailTemplate.domain}?utm_campaign=${shortLinkResult.shortLinkId}`;
      this.logger.log(`🔧 Step 2: Building Dub config with baseUrl=${baseUrl}`);

      const dubConfig = buildDubLinkConfig(
        baseUrl,
        shortLinkResult.shortLinkId || 'default',
        3, // 3 days expiration
      );

      this.logger.log(`🔧 Dub config: ${JSON.stringify(dubConfig, null, 2)}`);

      this.logger.log(`🌐 Step 3: Creating Dub short link...`);
      const dubResult = await this.dubLinkService.createShortLink(
        dubConfig.url,
        dubConfig,
      );

      let originalUrl = dubConfig.url; // Fallback to original URL

      if (dubResult.success && dubResult.data) {
        originalUrl = dubResult.data.shortLink;
        this.logger.log(`✅ Step 3 SUCCESS: Dub URL=${originalUrl}`);
      } else {
        this.logger.warn(
          `⚠️ Step 3 FAILED: ${dubResult.error}, using fallback URL=${originalUrl}`,
        );
      }

      // Step 4: Create tiny URL from Dub URL (or original URL if Dub failed)
      this.logger.log(`🔗 Step 4: Creating tiny URL from ${originalUrl}...`);
      const tinyUrlResult =
        await this.tinyUrlService.createTinyUrl(originalUrl);

      if (!tinyUrlResult.success) {
        this.logger.error(`❌ Step 4 FAILED: ${tinyUrlResult.error}`);
        return {
          success: false,
          originalUrl,
          error: `Failed to create tiny URL: ${tinyUrlResult.error}`,
        };
      }

      this.logger.log(
        `✅ Step 4 SUCCESS: Tiny URL=${tinyUrlResult.data?.tinyUrl}`,
      );

      const result = {
        success: true,
        originalUrl,
        dubUrl: dubResult.success ? dubResult.data?.shortLink : undefined,
        tinyUrl: tinyUrlResult.data?.tinyUrl,
        shortLinkId: shortLinkResult.shortLinkId,
      };

      this.logger.log(
        `🎉 Download URL creation COMPLETE: ${JSON.stringify(result, null, 2)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`💥 Download URL creation CRASHED:`, error);
      return {
        success: false,
        error: `Failed to create download URL: ${error.message}`,
      };
    }
  }

  /**
   * Create file short link using FileShortLinksService
   */
  private async createFileShortLink(
    emailTemplate: any,
  ): Promise<{ success: boolean; shortLinkId?: string; error?: string }> {
    try {
      this.logger.log(`📎 Creating file short link for template...`);

      // Check if email template has a file
      if (!emailTemplate.file) {
        this.logger.error(`❌ Email template missing file property`);
        return {
          success: false,
          error: 'Email template does not have a file attached',
        };
      }

      this.logger.log(`📁 Template file URL: ${emailTemplate.file}`);

      // Create a minimal mock file object
      const mockFile: Express.Multer.File = {
        path: emailTemplate.file,
        originalname: 'template-file',
        mimetype: 'application/octet-stream',
      } as Express.Multer.File;

      const createFileShortLinkDTO = {
        prefixPass: emailTemplate.prefix || 'download',
        brand: emailTemplate.brand,
        expirationLink: emailTemplate.expirationLink,
        quantity: '1', // Create only one short link
        popup: false,
        countryCode: '',
        job: '',
      };

      this.logger.log(
        `📋 FileShortLink DTO: ${JSON.stringify(createFileShortLinkDTO, null, 2)}`,
      );
      this.logger.log(
        `📄 Mock file: path=${mockFile.path}, originalname=${mockFile.originalname}`,
      );

      this.logger.log(
        `🔄 Calling fileShortLinksService.createFileShortLink...`,
      );
      const result = await this.fileShortLinksService.createFileShortLink({
        createFileShortLinkDTO,
        file: mockFile,
        authUser: undefined, // No auth user needed for email processing
      });

      this.logger.log(
        `📊 FileShortLink service result: ${JSON.stringify(result, null, 2)}`,
      );

      if (
        result &&
        result.fileShortLinkMeta &&
        result.fileShortLinkMeta.length > 0
      ) {
        const shortLinkId = result.fileShortLinkMeta[0].shortLinkId;
        this.logger.log(
          `✅ File short link created successfully: ${shortLinkId}`,
        );
        return {
          success: true,
          shortLinkId,
        };
      }

      this.logger.error(
        `❌ Invalid result from fileShortLinksService: result=${!!result}, meta=${result?.fileShortLinkMeta?.length || 0}`,
      );
      return {
        success: false,
        error: 'Failed to create file short link - invalid service response',
      };
    } catch (error) {
      this.logger.error('💥 File short link creation crashed:', error);
      return {
        success: false,
        error: `Failed to create file short link: ${error.message}`,
      };
    }
  }
}
