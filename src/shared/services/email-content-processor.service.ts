import { Injectable, Logger } from '@nestjs/common';
import { TinyUrlService } from './tiny-url.service';
import { FileShortLinksService } from 'src/modules/file-short-links/file-short-links.service';
import { DubLinkService } from './dub';

export interface DownloadUrlResult {
  success: boolean;
  originalUrl?: string;
  dubUrl?: string;
  tinyUrl?: string;
  shortLinkId?: string;
  error?: string;
}

/**
 * Build Dub link configuration
 * IMPROVED: Better URL handling and validation
 */
export const buildDubLinkConfig = (
  url: string,
  shortLinkId: string,
  expirationDays: number = 3,
) => {
  // Ensure URL has proper protocol
  let formattedUrl = url;
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    formattedUrl = `https://${url}`;
  }

  return {
    url: formattedUrl,
    externalId: shortLinkId,
    expiresAt: new Date(
      Date.now() + expirationDays * 24 * 60 * 60 * 1000,
    ).toISOString(),
    utm_campaign: shortLinkId,
    domain: 'dub.sh',
  };
};

@Injectable()
export class EmailContentProcessorService {
  private readonly logger = new Logger(EmailContentProcessorService.name);

  constructor(
    private readonly tinyUrlService: TinyUrlService,
    private readonly fileShortLinksService: FileShortLinksService,
    private readonly dubLinkService: DubLinkService,
  ) {}

  /**
   * Process email content and replace DOWNLOAD_URL placeholder with tiny URL
   */
  async processEmailContent(
    emailContent: string,
    emailTemplate: any,
  ): Promise<{ content: string; downloadUrlResult?: DownloadUrlResult }> {
    this.logger.log(
      `🔍 Processing email content for template: ${JSON.stringify(emailContent, null, 2)}`,
    );
    try {
      // Check if content contains DOWNLOAD_URL placeholder
      if (!emailContent.includes('{{DOWNLOAD_URL}}')) {
        return { content: emailContent };
      }

      // Create download URL
      const downloadUrlResult = await this.createDownloadUrl(emailTemplate);

      this.logger.log(
        `📦 Download URL creation result: ${JSON.stringify(downloadUrlResult, null, 2)}`,
      );

      if (!downloadUrlResult.success) {
        return {
          content: emailContent.replace(
            /\{\{DOWNLOAD_URL\}\}/g,
            downloadUrlResult.tinyUrl || '#',
          ),
          downloadUrlResult,
        };
      }

      // Replace placeholder with actual tiny URL
      const processedContent = emailContent.replace(
        /\{\{DOWNLOAD_URL\}\}/g,
        downloadUrlResult.tinyUrl || downloadUrlResult.originalUrl || '#',
      );

      return {
        content: processedContent,
        downloadUrlResult,
      };
    } catch (error) {
      this.logger.error('Failed to process email content:', error);
      return {
        content: emailContent.replace(/\{\{DOWNLOAD_URL\}\}/g, '#'),
        downloadUrlResult: {
          success: false,
          error: `Failed to process email content: ${error.message}`,
        },
      };
    }
  }

  /**
   * Create download URL with UTM campaign and tiny URL
   * IMPROVED: Better error handling, cleaner logic, and more robust URL generation
   */
  private async createDownloadUrl(
    emailTemplate: any,
  ): Promise<DownloadUrlResult> {
    const templateName = emailTemplate.name || 'unknown';

    try {
      this.logger.log(
        `🔗 Starting download URL creation for template: ${templateName}`,
      );
      this.logger.log(
        `📋 Template: domain=${emailTemplate.domain}, file=${emailTemplate.file}, prefix=${emailTemplate.prefix}`,
      );

      // Validate required template fields
      if (!emailTemplate.domain || !emailTemplate.file) {
        const error = `Missing required fields: domain=${!!emailTemplate.domain}, file=${!!emailTemplate.file}`;
        this.logger.error(`❌ Validation FAILED: ${error}`);
        return {
          success: false,
          error: `Template validation failed: ${error}`,
        };
      }

      // Step 1: Create file short link
      this.logger.log(`📎 Step 1: Creating file short link...`);
      const shortLinkResult = await this.createFileShortLink(emailTemplate);

      if (!shortLinkResult.success) {
        this.logger.error(`❌ Step 1 FAILED: ${shortLinkResult.error}`);
        return {
          success: false,
          error: `File short link creation failed: ${shortLinkResult.error}`,
        };
      }

      const shortLinkId = shortLinkResult.shortLinkId;
      if (!shortLinkId) {
        this.logger.error(`❌ Step 1 FAILED: shortLinkId is undefined`);
        return {
          success: false,
          error: `File short link creation failed: shortLinkId is undefined`,
        };
      }

      this.logger.log(`✅ Step 1 SUCCESS: shortLinkId=${shortLinkId}`);

      // Step 2: Build base URL with UTM campaign
      const baseUrl = this.buildBaseUrl(emailTemplate.domain, shortLinkId);
      this.logger.log(`🔧 Step 2: Built base URL: ${baseUrl}`);

      // Step 3: Create Dub short link (with graceful fallback)
      let finalUrl = baseUrl; // Default fallback
      const dubResult = await this.createDubShortLink(baseUrl, shortLinkId);

      if (dubResult.success && dubResult.data?.shortLink) {
        finalUrl = dubResult.data.shortLink;
        this.logger.log(`✅ Step 3 SUCCESS: Dub URL=${finalUrl}`);
      } else {
        this.logger.warn(
          `⚠️ Step 3 FAILED: ${dubResult.error}, using base URL as fallback`,
        );
      }

      // Step 4: Create TinyURL (final step)
      this.logger.log(`🔗 Step 4: Creating TinyURL from ${finalUrl}...`);
      const tinyUrlResult = await this.tinyUrlService.createTinyUrl(finalUrl);

      if (!tinyUrlResult.success) {
        this.logger.error(`❌ Step 4 FAILED: ${tinyUrlResult.error}`);
        // Return partial success with fallback URL
        return {
          success: false,
          originalUrl: baseUrl,
          dubUrl: dubResult.success ? dubResult.data?.shortLink : undefined,
          tinyUrl: finalUrl, // Use Dub URL or base URL as fallback
          shortLinkId: shortLinkId,
          error: `TinyURL creation failed: ${tinyUrlResult.error}`,
        };
      }

      const tinyUrl = tinyUrlResult.data?.tinyUrl;
      this.logger.log(`✅ Step 4 SUCCESS: TinyURL=${tinyUrl}`);

      // Final result
      const result = {
        success: true,
        originalUrl: baseUrl,
        dubUrl: dubResult.success ? dubResult.data?.shortLink : undefined,
        tinyUrl: tinyUrl,
        shortLinkId: shortLinkId,
      };

      this.logger.log(
        `🎉 Download URL creation COMPLETE: ${JSON.stringify(result, null, 2)}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `💥 Download URL creation CRASHED for template ${templateName}:`,
        error,
      );
      return {
        success: false,
        error: `Download URL creation failed: ${error.message}`,
      };
    }
  }

  /**
   * Build base URL with proper formatting and UTM campaign
   */
  private buildBaseUrl(domain: string, shortLinkId: string): string {
    // Ensure domain has proper protocol
    let formattedDomain = domain;
    if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
      formattedDomain = `https://${domain}`;
    }

    // Add UTM campaign parameter
    const separator = domain.includes('?') ? '&' : '?';
    return `${formattedDomain}${separator}utm_campaign=${shortLinkId}`;
  }

  /**
   * Create Dub short link with proper error handling
   */
  private async createDubShortLink(
    url: string,
    shortLinkId: string,
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      this.logger.log(`🌐 Step 3: Creating Dub short link...`);

      const dubConfig = buildDubLinkConfig(url, shortLinkId, 3);
      this.logger.log(`🔧 Dub config: ${JSON.stringify(dubConfig, null, 2)}`);

      const result = await this.dubLinkService.createShortLink(
        dubConfig.url,
        dubConfig,
      );

      if (result.success) {
        this.logger.log(`✅ Dub short link created: ${result.data?.shortLink}`);
      } else {
        this.logger.warn(`⚠️ Dub short link failed: ${result.error}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`💥 Dub short link creation crashed:`, error);
      return {
        success: false,
        error: `Dub service error: ${error.message}`,
      };
    }
  }

  /**
   * Create file short link using FileShortLinksService
   */
  private async createFileShortLink(
    emailTemplate: any,
  ): Promise<{ success: boolean; shortLinkId?: string; error?: string }> {
    try {
      this.logger.log(`📎 Creating file short link for template...`);

      // Check if email template has a file
      if (!emailTemplate.file) {
        this.logger.error(`❌ Email template missing file property`);
        return {
          success: false,
          error: 'Email template does not have a file attached',
        };
      }

      this.logger.log(`📁 Template file URL: ${emailTemplate.file}`);

      // Create a minimal mock file object
      const mockFile: Express.Multer.File = {
        path: emailTemplate.file,
        originalname: 'template-file',
        mimetype: 'application/octet-stream',
      } as Express.Multer.File;

      const createFileShortLinkDTO = {
        prefixPass: emailTemplate.prefix || 'download',
        brand: emailTemplate.brand,
        expirationLink: emailTemplate.expirationLink,
        quantity: '1', // Create only one short link
        popup: false,
        countryCode: '',
        job: '',
      };

      this.logger.log(
        `📋 FileShortLink DTO: ${JSON.stringify(createFileShortLinkDTO, null, 2)}`,
      );
      this.logger.log(
        `📄 Mock file: path=${mockFile.path}, originalname=${mockFile.originalname}`,
      );

      this.logger.log(
        `🔄 Calling fileShortLinksService.createFileShortLink...`,
      );
      const result = await this.fileShortLinksService.createFileShortLink({
        createFileShortLinkDTO,
        file: mockFile,
        authUser: undefined, // No auth user needed for email processing
      });

      this.logger.log(
        `📊 FileShortLink service result: ${JSON.stringify(result, null, 2)}`,
      );

      if (
        result &&
        result.fileShortLinkMeta &&
        result.fileShortLinkMeta.length > 0
      ) {
        const shortLinkId = result.fileShortLinkMeta[0].shortLinkId;
        this.logger.log(
          `✅ File short link created successfully: ${shortLinkId}`,
        );
        return {
          success: true,
          shortLinkId,
        };
      }

      this.logger.error(
        `❌ Invalid result from fileShortLinksService: result=${!!result}, meta=${result?.fileShortLinkMeta?.length || 0}`,
      );
      return {
        success: false,
        error: 'Failed to create file short link - invalid service response',
      };
    } catch (error) {
      this.logger.error('💥 File short link creation crashed:', error);
      return {
        success: false,
        error: `Failed to create file short link: ${error.message}`,
      };
    }
  }
}
