import { Injectable, Logger } from '@nestjs/common';
import { TinyUrlService } from './tiny-url.service';
import { FileShortLinksService } from 'src/modules/file-short-links/file-short-links.service';
import { DubLinkService } from './dub';

export interface DownloadUrlResult {
  success: boolean;
  originalUrl?: string;
  dubUrl?: string;
  tinyUrl?: string;
  shortLinkId?: string;
  error?: string;
}

/**
 * Build Dub link configuration
 */
export const buildDubLinkConfig = (
  url: string,
  shortLinkId: string,
  expirationDays: number = 3,
) => ({
  url: `https://${url}`,
  externalId: shortLinkId,
  expiresAt: new Date(
    Date.now() + expirationDays * 24 * 60 * 60 * 1000,
  ).toISOString(),
  utm_campaign: shortLinkId,
  domain: 'dub.sh',
});

@Injectable()
export class EmailContentProcessorService {
  private readonly logger = new Logger(EmailContentProcessorService.name);

  constructor(
    private readonly tinyUrlService: TinyUrlService,
    private readonly fileShortLinksService: FileShortLinksService,
    private readonly dubLinkService: DubLinkService,
  ) {}

  /**
   * Process email content and replace DOWNLOAD_URL placeholder with tiny URL
   */
  async processEmailContent(
    emailContent: string,
    emailTemplate: any,
  ): Promise<{ content: string; downloadUrlResult?: DownloadUrlResult }> {
    try {
      // Check if DOWNLOAD_URL is in requirements
      if (!emailTemplate.requirements?.includes('DOWNLOAD_URL')) {
        return { content: emailContent };
      }

      // Check if content contains DOWNLOAD_URL placeholder
      if (!emailContent.includes('{{DOWNLOAD_URL}}')) {
        return { content: emailContent };
      }

      // Create download URL
      const downloadUrlResult = await this.createDownloadUrl(emailTemplate);

      if (!downloadUrlResult.success) {
        return {
          content: emailContent.replace(
            /\{\{DOWNLOAD_URL\}\}/g,
            downloadUrlResult.tinyUrl || '#',
          ),
          downloadUrlResult,
        };
      }

      // Replace placeholder with actual tiny URL
      const processedContent = emailContent.replace(
        /\{\{DOWNLOAD_URL\}\}/g,
        downloadUrlResult.tinyUrl || downloadUrlResult.originalUrl || '#',
      );

      return {
        content: processedContent,
        downloadUrlResult,
      };
    } catch (error) {
      this.logger.error('Failed to process email content:', error);
      return {
        content: emailContent.replace(/\{\{DOWNLOAD_URL\}\}/g, '#'),
        downloadUrlResult: {
          success: false,
          error: `Failed to process email content: ${error.message}`,
        },
      };
    }
  }

  /**
   * Create download URL with UTM campaign and tiny URL
   */
  private async createDownloadUrl(
    emailTemplate: any,
  ): Promise<DownloadUrlResult> {
    try {
      // Create file short link first
      const shortLinkResult = await this.createFileShortLink(emailTemplate);

      if (!shortLinkResult.success) {
        return {
          success: false,
          error: `Failed to create short link: ${shortLinkResult.error}`,
        };
      }

      // Step 1: Build Dub config and get original URL from it
      const dubConfig = buildDubLinkConfig(
        `${emailTemplate.domain}?utm_campaign=${shortLinkResult.shortLinkId}`,
        shortLinkResult.shortLinkId || 'default',
        3, // 3 days expiration
      );

      const dubResult = await this.dubLinkService.createShortLink(
        dubConfig.url,
        dubConfig,
      );

      let originalUrl = dubConfig.url; // Fallback to original URL

      if (dubResult.success && dubResult.data) {
        originalUrl = dubResult.data.shortLink;
      } else {
        this.logger.warn(
          `Failed to create Dub link: ${dubResult.error}, using original URL`,
        );
        // originalUrl is already set to dubConfig.url as fallback
      }

      // Step 2: Create tiny URL from Dub URL (or original URL if Dub failed)
      const tinyUrlResult =
        await this.tinyUrlService.createTinyUrl(originalUrl);

      if (!tinyUrlResult.success) {
        this.logger.error(`Failed to create tiny URL: ${tinyUrlResult.error}`);
        return {
          success: false,
          originalUrl,
          error: `Failed to create tiny URL: ${tinyUrlResult.error}`,
        };
      }

      return {
        success: true,
        originalUrl,
        dubUrl: dubResult.success ? dubResult.data?.shortLink : undefined,
        tinyUrl: tinyUrlResult.data?.tinyUrl,
        shortLinkId: shortLinkResult.shortLinkId,
      };
    } catch (error) {
      this.logger.error(`Failed to create download URL:`, error);
      return {
        success: false,
        error: `Failed to create download URL: ${error.message}`,
      };
    }
  }

  /**
   * Create file short link using FileShortLinksService
   */
  private async createFileShortLink(
    emailTemplate: any,
  ): Promise<{ success: boolean; shortLinkId?: string; error?: string }> {
    try {
      // Check if email template has a file
      if (!emailTemplate.file) {
        return {
          success: false,
          error: 'Email template does not have a file attached',
        };
      }

      // Create a minimal mock file object
      const mockFile: Express.Multer.File = {
        path: emailTemplate.file,
        originalname: 'template-file',
        mimetype: 'application/octet-stream',
      } as Express.Multer.File;

      const createFileShortLinkDTO = {
        prefixPass: emailTemplate.prefix || 'download',
        brand: emailTemplate.brand,
        expirationLink: emailTemplate.expirationLink,
        quantity: '1', // Create only one short link
        popup: false,
        countryCode: '',
        job: '',
      };

      const result = await this.fileShortLinksService.createFileShortLink({
        createFileShortLinkDTO,
        file: mockFile,
        authUser: undefined, // No auth user needed for email processing
      });

      if (
        result &&
        result.fileShortLinkMeta &&
        result.fileShortLinkMeta.length > 0
      ) {
        return {
          success: true,
          shortLinkId: result.fileShortLinkMeta[0].shortLinkId,
        };
      }

      return {
        success: false,
        error: 'Failed to create file short link',
      };
    } catch (error) {
      this.logger.error('Failed to create file short link:', error);
      return {
        success: false,
        error: `Failed to create file short link: ${error.message}`,
      };
    }
  }
}
