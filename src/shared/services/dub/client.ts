import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DubLink,
  CreateLinkParams,
  GetLinksParams,
  GetLinksResponse,
} from './types';

@Injectable()
export class DubService {
  private readonly logger = new Logger(DubService.name);
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.dub.co';

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('DUB_API_KEY') || '';
    if (!this.apiKey) {
      this.logger.warn('DUB_API_KEY not configured');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    if (!this.apiKey) {
      throw new Error('DUB_API_KEY not configured');
    }

    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.apiKey}`,
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: 'Unknown error' }));
        this.logger.error(
          `Dub API error: ${response.status} ${response.statusText}`,
          errorData,
        );
        throw new Error(
          `Dub API error: ${errorData.error || response.statusText}`,
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      this.logger.error(`Failed to make request to Dub API:`, error);
      throw error;
    }
  }

  /**
   * Create a new short link
   */
  async createLink(params: CreateLinkParams): Promise<DubLink> {
    return this.request<DubLink>('/links', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  /**
   * Get a link by ID or key
   */
  async getLink(linkId: string): Promise<DubLink> {
    return this.request<DubLink>(`/links/${linkId}`);
  }

  /**
   * Get analytics for a link
   */
  async getLinkAnalytics(linkId: string): Promise<any> {
    return this.request<any>(`/links/${linkId}/analytics`);
  }

  /**
   * Update a link
   */
  async updateLink(
    linkId: string,
    params: Partial<CreateLinkParams>,
  ): Promise<DubLink> {
    return this.request<DubLink>(`/links/${linkId}`, {
      method: 'PATCH',
      body: JSON.stringify(params),
    });
  }

  /**
   * Delete a link
   */
  async deleteLink(linkId: string): Promise<void> {
    await this.request(`/links/${linkId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get all links for the workspace
   */
  async getLinks(params?: GetLinksParams): Promise<GetLinksResponse> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.request<GetLinksResponse>(`/links${query}`);
  }

  /**
   * Check if service is configured and available
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Health check for Dub API
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConfigured()) {
        return false;
      }

      // Try to get links with limit 1 to test API connectivity
      await this.getLinks({ limit: 1 });
      return true;
    } catch (error) {
      this.logger.error('Dub API health check failed:', error);
      return false;
    }
  }
}
