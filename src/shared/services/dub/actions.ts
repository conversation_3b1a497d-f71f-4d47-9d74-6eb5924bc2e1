import { Injectable, Logger } from '@nestjs/common';
import { DubService } from './client';
import { CreateLinkParams, DubLink } from './types';

@Injectable()
export class DubLinkService {
  private readonly logger = new Logger(DubLinkService.name);

  constructor(private readonly dubService: DubService) {}

  /**
   * Create a short link using Dub API
   */
  async createShortLink(
    url: string,
    options?: Partial<CreateLinkParams>,
  ): Promise<{
    success: boolean;
    data?: DubLink;
    error?: string;
  }> {
    try {
      this.logger.log(`🌐 Creating Dub short link for: ${url}`);
      this.logger.log(`⚙️ Options: ${JSON.stringify(options, null, 2)}`);

      if (!this.dubService.isConfigured()) {
        this.logger.warn(
          '❌ Dub service not configured, skipping link creation',
        );
        return {
          success: false,
          error: 'Dub service not configured',
        };
      }

      this.logger.log(`✅ Dub service is configured`);

      const linkParams = {
        url,
        domain: 'dub.sh', // Default domain
        ...options,
      };

      this.logger.log(
        `📤 Calling Dub API with params: ${JSON.stringify(linkParams, null, 2)}`,
      );

      const link = await this.dubService.createLink(linkParams);

      this.logger.log(`📥 Dub API response: ${JSON.stringify(link, null, 2)}`);
      this.logger.log(
        `✅ Dub short link created successfully: ${link.shortLink}`,
      );

      return {
        success: true,
        data: link,
      };
    } catch (error) {
      this.logger.error('💥 Failed to create Dub short link:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create short link',
      };
    }
  }

  /**
   * Create multiple short links in batch
   */
  async createBatchLinks(
    urls: Array<{
      url: string;
      key?: string;
      options?: Partial<CreateLinkParams>;
    }>,
  ): Promise<
    Array<{
      url: string;
      success: boolean;
      data?: DubLink;
      error?: string;
    }>
  > {
    const results: Array<{
      url: string;
      success: boolean;
      data?: DubLink;
      error?: string;
    }> = [];

    for (const { url, key, options } of urls) {
      const result = await this.createShortLink(url, { key, ...options });
      results.push({
        url,
        ...result,
      });
    }

    return results;
  }

  /**
   * Get all links for the workspace
   */
  async getLinks(params?: {
    limit?: number;
    page?: number;
    tagId?: string;
    search?: string;
    sort?: string;
    archived?: boolean;
  }): Promise<{
    success: boolean;
    data?: { links: DubLink[]; totalLinks: number };
    error?: string;
  }> {
    try {
      if (!this.dubService.isConfigured()) {
        return {
          success: false,
          error: 'Dub service not configured',
        };
      }

      const result = await this.dubService.getLinks(params);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error('Failed to get links:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get links',
      };
    }
  }

  /**
   * Get link by ID
   */
  async getLinkById(linkId: string): Promise<{
    success: boolean;
    data?: DubLink;
    error?: string;
  }> {
    try {
      if (!this.dubService.isConfigured()) {
        return {
          success: false,
          error: 'Dub service not configured',
        };
      }

      const link = await this.dubService.getLink(linkId);
      return {
        success: true,
        data: link,
      };
    } catch (error) {
      this.logger.error(`Failed to get link ${linkId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get link',
      };
    }
  }

  /**
   * Delete a link
   */
  async deleteLink(linkId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      if (!this.dubService.isConfigured()) {
        return {
          success: false,
          error: 'Dub service not configured',
        };
      }

      await this.dubService.deleteLink(linkId);
      return {
        success: true,
      };
    } catch (error) {
      this.logger.error(`Failed to delete link ${linkId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete link',
      };
    }
  }

  /**
   * Update a link
   */
  async updateLink(
    linkId: string,
    data: Partial<CreateLinkParams>,
  ): Promise<{
    success: boolean;
    data?: DubLink;
    error?: string;
  }> {
    try {
      if (!this.dubService.isConfigured()) {
        return {
          success: false,
          error: 'Dub service not configured',
        };
      }

      const link = await this.dubService.updateLink(linkId, data);
      return {
        success: true,
        data: link,
      };
    } catch (error) {
      this.logger.error(`Failed to update link ${linkId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update link',
      };
    }
  }

  /**
   * Check if Dub service is available
   */
  async isServiceAvailable(): Promise<boolean> {
    return this.dubService.healthCheck();
  }
}
