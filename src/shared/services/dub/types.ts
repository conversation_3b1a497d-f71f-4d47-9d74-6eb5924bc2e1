export interface DubLink {
  id: string;
  domain: string;
  key: string;
  url: string;
  trackConversion: boolean;
  externalId: string | null;
  tenantId: string | null;
  programId: string | null;
  partnerId: string | null;
  archived: boolean;
  expiresAt: string | null;
  expiredUrl: string | null;
  password: string | null;
  proxy: boolean;
  title: string | null;
  description: string | null;
  image: string | null;
  video: string | null;
  rewrite: boolean;
  doIndex: boolean;
  ios: string | null;
  android: string | null;
  geo: Record<string, string> | null;
  publicStats: boolean;
  tags: Array<{
    id: string;
    name: string;
    color: string;
  }> | null;
  folderId: string | null;
  webhookIds: string[];
  comments: string | null;
  shortLink: string;
  qrCode: string;
  utm_source: string | null;
  utm_medium: string | null;
  utm_campaign: string | null;
  utm_term: string | null;
  utm_content: string | null;
  testVariants?: Array<{
    url: string;
    percentage: number;
  }> | null;
  testStartedAt?: string | null;
  testCompletedAt?: string | null;
  userId: string | null;
  workspaceId: string;
  clicks: number;
  lastClicked: string | null;
  leads: number;
  sales: number;
  saleAmount: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateLinkParams {
  url: string;
  domain?: string;
  key?: string;
  externalId?: string;
  tenantId?: string;
  programId?: string;
  partnerId?: string;
  prefix?: string;
  trackConversion?: boolean;
  archived?: boolean;
  tagIds?: string[];
  tagNames?: string[];
  folderId?: string | null;
  comments?: string | null;
  expiresAt?: string | null;
  expiredUrl?: string | null;
  password?: string | null;
  proxy?: boolean;
  title?: string | null;
  description?: string | null;
  image?: string | null;
  video?: string | null;
  rewrite?: boolean;
  ios?: string | null;
  android?: string | null;
  geo?: Record<string, string> | null;
  doIndex?: boolean;
  utm_source?: string | null;
  utm_medium?: string | null;
  utm_campaign?: string | null;
  utm_term?: string | null;
  utm_content?: string | null;
  ref?: string | null;
  webhookIds?: string[] | null;
  testVariants?: Array<{
    url: string;
    percentage: number;
  }> | null;
  testStartedAt?: string | null;
  testCompletedAt?: string | null;
}

export interface GetLinksParams {
  limit?: number;
  page?: number;
  tagId?: string;
  search?: string;
  sort?: string;
  archived?: boolean;
}

export interface GetLinksResponse {
  links: DubLink[];
  totalLinks: number;
}

export interface DubApiError {
  error: string;
  message: string;
  code?: string;
}
