import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { EmailQuotaService } from './email-quota.service';

// Interfaces for API requests and responses
export interface SendEmailRequest {
  toEmail: string;
  subject: string;
  htmlContent: string;
  fromName: string;
  fromEmail: string;
  replacements?: Record<string, string>;
  configSet?: string;
  campaign?: string;
  webhookUrl?: string;
  emailType?: string;
}

export interface SendEmailResponse {
  status: string;
  message: string;
  data: {
    messageId: string;
    response: string;
  };
  quota: {
    max24HourSend: number;
    sentLast24Hours: number;
    maxSendRate: number;
    remainingToday: number;
    usagePercentage: string;
  };
  accountStatus: {
    enabled: boolean;
    productionAccess: boolean;
    reputationStatus: string;
  };
  deliveryMetrics: {
    timestamp: string;
    deliveryAttempts: number;
    delivered: number;
    deliveryRate: string;
    bounces: number;
    bounceRate: string;
    complaints: number;
    complaintRate: string;
    rejects: number;
    rejectRate: string;
  };
}

export interface EmailReputationResponse {
  quota: {
    max24HourSend: number;
    sentLast24Hours: number;
    maxSendRate: number;
    remainingToday: number;
    usagePercentage: string;
  };
  accountStatus: {
    enabled: boolean;
    productionAccess: boolean;
    reputationStatus: string;
  };
  deliveryMetrics: {
    totalDeliveryAttempts: number;
    totalDelivered: number;
    deliveryRate: string;
    totalBounces: number;
    bounceRate: string;
    totalComplaints: number;
    complaintRate: string;
    totalRejects: number;
    rejectRate: string;
  };
  configurationSets: Array<{
    Name: string;
    ReputationOptions: {
      ReputationMetricsEnabled: boolean;
      LastFreshStart: string;
    };
  }>;
  lastUpdated: string;
}

export interface EmailServiceError {
  status: string;
  message: string;
  error?: string;
  missingPlaceholders?: string[];
  retryAfter?: number;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly emailQuotaService: EmailQuotaService,
  ) {
    this.baseUrl = this.configService.get<string>(
      'EMAIL_SERVICE_BASE_URL',
      'http://x54.run/api',
    );
    this.apiKey = this.configService.get<string>('EMAIL_SERVICE_API_KEY', '');

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
      },
    });

    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        return config;
      },
      (error) => {
        this.logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      },
    );

    // Response interceptor for logging
    this.httpClient.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        this.logger.error(
          'Response interceptor error:',
          error.response?.data || error.message,
        );
        return Promise.reject(error);
      },
    );
  }

  /**
   * Send email using the external email service with quota and rate limiting
   */
  async sendEmail(
    emailData: SendEmailRequest,
    campId?: string,
  ): Promise<SendEmailResponse> {
    try {
      this.logger.log(
        `Sending email to ${emailData.toEmail} with subject "${emailData.subject}"`,
      );

      // Check quota and rate limits before sending
      if (campId) {
        const canSend = await this.emailQuotaService.canSendEmail(campId);
        if (!canSend.canSend) {
          const error = new Error(`Cannot send email: ${canSend.reason}`);
          (error as any).waitTime = canSend.waitTime;
          (error as any).reason = canSend.reason;
          throw error;
        }
      }

      // Validate required fields
      this.validateEmailRequest(emailData);

      // Clean and validate email data to prevent JSON issues
      const cleanEmailData = this.cleanEmailData(emailData);

      // Validate that the data can be properly serialized to JSON
      this.validateJsonSerialization(cleanEmailData);

      // Send request with proper JSON handling
      const response: AxiosResponse<SendEmailResponse> =
        await this.httpClient.post('/email/send', cleanEmailData, {
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
          },
          // Ensure proper JSON serialization
          transformRequest: [
            (data) => {
              try {
                return JSON.stringify(data);
              } catch (error) {
                this.logger.error('JSON serialization failed:', error);
                throw new Error(
                  `Failed to serialize email data: ${error.message}`,
                );
              }
            },
          ],
        });

      // Record email sent for quota tracking
      if (campId) {
        await this.emailQuotaService.recordEmailSent(campId);
      } else {
        this.logger.warn(`⚠️ No campId provided, skipping quota tracking`);
      }

      return response.data;
    } catch (error) {
      this.handleEmailError(error, emailData.toEmail);
      throw error;
    }
  }

  /**
   * Send bulk emails to multiple recipients
   */
  async sendBulkEmails(
    emails: Omit<SendEmailRequest, 'toEmail'> & { recipients: string[] },
  ): Promise<{
    successful: string[];
    failed: Array<{ email: string; error: string }>;
  }> {
    const { recipients, ...emailTemplate } = emails;
    const successful: string[] = [];
    const failed: Array<{ email: string; error: string }> = [];

    // Process emails in batches to avoid overwhelming the service
    const batchSize = 10;
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      const promises = batch.map(async (email) => {
        try {
          await this.sendEmail({ ...emailTemplate, toEmail: email });
          successful.push(email);
        } catch (error) {
          const errorMessage = this.extractErrorMessage(error);
          failed.push({ email, error: errorMessage });
          this.logger.warn(`Failed to send email to ${email}: ${errorMessage}`);
        }
      });

      await Promise.allSettled(promises);

      // Add delay between batches to respect rate limits
      if (i + batchSize < recipients.length) {
        await this.delay(1000); // 1 second delay between batches
      }
    }

    return { successful, failed };
  }

  /**
   * Get email service reputation and account status
   */
  async getEmailReputation(): Promise<EmailReputationResponse> {
    try {
      const response = await this.httpClient.get('/reputation');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to fetch email reputation:', error);
      throw error;
    }
  }

  /**
   * Send templated email with replacements
   */
  async sendTemplatedEmail(
    toEmail: string,
    templateData: {
      subject: string;
      htmlTemplate: string;
      fromName: string;
      fromEmail: string;
      replacements: Record<string, string>;
      configSet?: string;
      campaign?: string;
    },
  ): Promise<SendEmailResponse> {
    const emailData: SendEmailRequest = {
      toEmail,
      subject: templateData.subject,
      htmlContent: templateData.htmlTemplate,
      fromName: templateData.fromName,
      fromEmail: templateData.fromEmail,
      replacements: templateData.replacements,
      configSet: templateData.configSet,
      campaign: templateData.campaign,
    };

    return this.sendEmail(emailData);
  }

  /**
   * Check if email service is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const reputation = await this.getEmailReputation();
      return (
        reputation.accountStatus.enabled &&
        reputation.accountStatus.reputationStatus === 'healthy'
      );
    } catch (error) {
      this.logger.error('Email service health check failed:', error);
      return false;
    }
  }

  /**
   * Clean email data to ensure proper JSON serialization
   */
  private cleanEmailData(emailData: SendEmailRequest): SendEmailRequest {
    return {
      ...emailData,
      // Ensure all string fields are clean and valid
      subject: this.cleanString(emailData.subject),
      htmlContent: this.cleanHtmlContent(emailData.htmlContent),
      fromName: this.cleanString(emailData.fromName),
      fromEmail: this.cleanString(emailData.fromEmail),
      toEmail: this.cleanString(emailData.toEmail),
      // Handle optional fields
      campaign: emailData.campaign
        ? this.cleanString(emailData.campaign)
        : emailData.campaign,
      configSet: emailData.configSet
        ? this.cleanString(emailData.configSet)
        : emailData.configSet,
      webhookUrl: emailData.webhookUrl
        ? this.cleanString(emailData.webhookUrl)
        : emailData.webhookUrl,
      emailType: emailData.emailType
        ? this.cleanString(emailData.emailType)
        : emailData.emailType,
      // Keep replacements as-is since they're already an object
      replacements: emailData.replacements,
    };
  }

  /**
   * Clean string to ensure it's JSON-safe without double escaping
   */
  private cleanString(str: string): string {
    if (!str) return str;

    // Remove null bytes and other problematic characters that can break JSON parsing
    // But don't escape quotes/newlines since Axios will handle JSON serialization
    return str
      .replace(/\0/g, '') // Remove null bytes
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters except \t, \n, \r
      .trim(); // Remove leading/trailing whitespace
  }

  /**
   * Clean HTML content to ensure it's JSON-safe
   */
  private cleanHtmlContent(htmlContent: string): string {
    if (!htmlContent) return htmlContent;

    // For HTML content, preserve structure but remove problematic characters
    return htmlContent
      .replace(/\0/g, '') // Remove null bytes
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // Remove control characters except \t, \n, \r
  }

  /**
   * Validate that email data can be properly serialized to JSON
   */
  private validateJsonSerialization(emailData: SendEmailRequest): void {
    try {
      // Test JSON serialization
      const jsonString = JSON.stringify(emailData);

      // Test JSON parsing to ensure round-trip works
      JSON.parse(jsonString);
    } catch (error) {
      this.logger.error('JSON serialization validation failed:', error);
      this.logger.error('Problematic email data:', {
        toEmail: emailData.toEmail,
        subject: emailData.subject?.substring(0, 50) + '...',
        htmlContentLength: emailData.htmlContent?.length,
        fromName: emailData.fromName,
        fromEmail: emailData.fromEmail,
      });
      throw new Error(
        `Email data cannot be serialized to JSON: ${error.message}`,
      );
    }
  }

  /**
   * Validate email request data
   */
  private validateEmailRequest(emailData: SendEmailRequest): void {
    const requiredFields = [
      'toEmail',
      'subject',
      'htmlContent',
      'fromName',
      'fromEmail',
    ];

    // Check for missing or empty fields
    const missingFields = requiredFields.filter((field) => {
      const value = emailData[field as keyof SendEmailRequest];
      return !value || (typeof value === 'string' && value.trim() === '');
    });

    if (missingFields.length > 0) {
      this.logger.error(
        `Email validation failed. Missing/empty fields: ${missingFields.join(', ')}`,
      );
      this.logger.error(`Email data: ${JSON.stringify(emailData)}`);
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailData.toEmail)) {
      throw new Error(`Invalid recipient email format: ${emailData.toEmail}`);
    }
    if (!emailRegex.test(emailData.fromEmail)) {
      throw new Error(`Invalid sender email format: ${emailData.fromEmail}`);
    }

    // Validate content length
    if (emailData.htmlContent.length > 1000000) {
      // 1MB limit
      throw new Error('HTML content too large (max 1MB)');
    }
  }

  /**
   * Handle email sending errors with detailed logging
   */
  private handleEmailError(error: any, toEmail: string): void {
    if (error.response) {
      this.logger.error(
        `Email failed [${error.response.status}] ${toEmail}: ${error.response.data?.message || error.message}`,
      );
    } else if (error.request) {
      this.logger.error(
        `Email failed [NO_RESPONSE] ${toEmail}: No response from service`,
      );
    } else {
      this.logger.error(
        `Email failed [SETUP_ERROR] ${toEmail}: ${error.message}`,
      );
    }
  }

  /**
   * Extract error message from error object
   */
  private extractErrorMessage(error: any): string {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'Unknown error occurred';
  }

  /**
   * Delay utility for rate limiting
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Test JSON serialization with problematic content
   */
  async testJsonSerialization(): Promise<boolean> {
    try {
      const testData: SendEmailRequest = {
        toEmail: '<EMAIL>',
        subject: 'Test "Subject" with quotes',
        htmlContent: `<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
</head>
<body>
    <p>Test content with "quotes" and 'single quotes'</p>
    <script>console.log("test");</script>
</body>
</html>`,
        fromName: 'Test "Sender"',
        fromEmail: '<EMAIL>',
        campaign: 'test_campaign',
      };

      const cleanData = this.cleanEmailData(testData);
      this.validateJsonSerialization(cleanData);

      return true;
    } catch (error) {
      this.logger.error('JSON serialization test failed:', error);
      return false;
    }
  }
}
