import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EmailService, SendEmailRequest } from './email-service.service';

export interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  activationLink?: string;
  companyName?: string;
}

export interface PasswordResetEmailData {
  userEmail: string;
  userName: string;
  resetLink: string;
  expirationTime?: string;
}

export interface VerificationEmailData {
  userEmail: string;
  userName: string;
  verificationLink: string;
  expirationTime?: string;
}

export interface CampaignEmailData {
  userEmail: string;
  userName: string;
  campaignTitle: string;
  campaignContent: string;
  unsubscribeLink?: string;
}

export interface NotificationEmailData {
  userEmail: string;
  userName: string;
  notificationTitle: string;
  notificationContent: string;
  actionLink?: string;
  actionText?: string;
}

@Injectable()
export class EmailTemplateService {
  private readonly defaultFromEmail: string;
  private readonly defaultFromName: string;
  private readonly companyName: string;
  private readonly baseUrl: string;

  constructor(
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
  ) {
    this.defaultFromEmail = this.configService.get<string>('DEFAULT_FROM_EMAIL', '<EMAIL>');
    this.defaultFromName = this.configService.get<string>('DEFAULT_FROM_NAME', 'Your App');
    this.companyName = this.configService.get<string>('COMPANY_NAME', 'Your Company');
    this.baseUrl = this.configService.get<string>('BASE_URL', 'https://yourdomain.com');
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(data: WelcomeEmailData): Promise<void> {
    const htmlTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to ${data.companyName || this.companyName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to ${data.companyName || this.companyName}!</h1>
          </div>
          <div class="content">
            <h2>Hello {{userName}}!</h2>
            <p>Welcome to our platform! We're excited to have you on board.</p>
            <p>Your account has been successfully created and you can now start using our services.</p>
            {{#if activationLink}}
            <p>To get started, please activate your account by clicking the button below:</p>
            <a href="{{activationLink}}" class="button">Activate Account</a>
            {{/if}}
            <p>If you have any questions, feel free to contact our support team.</p>
            <p>Best regards,<br>The ${data.companyName || this.companyName} Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 ${data.companyName || this.companyName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: SendEmailRequest = {
      toEmail: data.userEmail,
      subject: `Welcome to ${data.companyName || this.companyName}!`,
      htmlContent: htmlTemplate,
      fromName: this.defaultFromName,
      fromEmail: this.defaultFromEmail,
      replacements: {
        userName: data.userName,
        activationLink: data.activationLink || '',
      },
      campaign: 'welcome-email',
    };

    await this.emailService.sendEmail(emailData);
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(data: PasswordResetEmailData): Promise<void> {
    const htmlTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Password Reset Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hello {{userName}},</h2>
            <p>We received a request to reset your password for your account.</p>
            <p>Click the button below to reset your password:</p>
            <a href="{{resetLink}}" class="button">Reset Password</a>
            <div class="warning">
              <strong>Important:</strong> This link will expire in {{expirationTime}}. If you didn't request this password reset, please ignore this email.
            </div>
            <p>For security reasons, please do not share this link with anyone.</p>
            <p>Best regards,<br>The ${this.companyName} Security Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 ${this.companyName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: SendEmailRequest = {
      toEmail: data.userEmail,
      subject: 'Password Reset Request',
      htmlContent: htmlTemplate,
      fromName: `${this.defaultFromName} Security`,
      fromEmail: this.defaultFromEmail,
      replacements: {
        userName: data.userName,
        resetLink: data.resetLink,
        expirationTime: data.expirationTime || '24 hours',
      },
      campaign: 'password-reset',
    };

    await this.emailService.sendEmail(emailData);
  }

  /**
   * Send email verification
   */
  async sendVerificationEmail(data: VerificationEmailData): Promise<void> {
    const htmlTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Verify Your Email</h1>
          </div>
          <div class="content">
            <h2>Hello {{userName}},</h2>
            <p>Thank you for signing up! Please verify your email address to complete your registration.</p>
            <p>Click the button below to verify your email:</p>
            <a href="{{verificationLink}}" class="button">Verify Email</a>
            <p><strong>This verification link will expire in {{expirationTime}}.</strong></p>
            <p>If you didn't create an account with us, please ignore this email.</p>
            <p>Best regards,<br>The ${this.companyName} Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 ${this.companyName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: SendEmailRequest = {
      toEmail: data.userEmail,
      subject: 'Please verify your email address',
      htmlContent: htmlTemplate,
      fromName: this.defaultFromName,
      fromEmail: this.defaultFromEmail,
      replacements: {
        userName: data.userName,
        verificationLink: data.verificationLink,
        expirationTime: data.expirationTime || '24 hours',
      },
      campaign: 'email-verification',
    };

    await this.emailService.sendEmail(emailData);
  }

  /**
   * Send campaign email
   */
  async sendCampaignEmail(data: CampaignEmailData): Promise<void> {
    const htmlTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>{{campaignTitle}}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #6f42c1; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .unsubscribe { margin-top: 20px; font-size: 11px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>{{campaignTitle}}</h1>
          </div>
          <div class="content">
            <h2>Hello {{userName}},</h2>
            {{campaignContent}}
          </div>
          <div class="footer">
            <p>&copy; 2024 ${this.companyName}. All rights reserved.</p>
            {{#if unsubscribeLink}}
            <div class="unsubscribe">
              <a href="{{unsubscribeLink}}">Unsubscribe from these emails</a>
            </div>
            {{/if}}
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: SendEmailRequest = {
      toEmail: data.userEmail,
      subject: data.campaignTitle,
      htmlContent: htmlTemplate,
      fromName: this.defaultFromName,
      fromEmail: this.defaultFromEmail,
      replacements: {
        userName: data.userName,
        campaignTitle: data.campaignTitle,
        campaignContent: data.campaignContent,
        unsubscribeLink: data.unsubscribeLink || '',
      },
      campaign: 'marketing-campaign',
    };

    await this.emailService.sendEmail(emailData);
  }

  /**
   * Send notification email
   */
  async sendNotificationEmail(data: NotificationEmailData): Promise<void> {
    const htmlTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>{{notificationTitle}}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #17a2b8; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>{{notificationTitle}}</h1>
          </div>
          <div class="content">
            <h2>Hello {{userName}},</h2>
            {{notificationContent}}
            {{#if actionLink}}
            <p><a href="{{actionLink}}" class="button">{{actionText}}</a></p>
            {{/if}}
            <p>Best regards,<br>The ${this.companyName} Team</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 ${this.companyName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const emailData: SendEmailRequest = {
      toEmail: data.userEmail,
      subject: data.notificationTitle,
      htmlContent: htmlTemplate,
      fromName: this.defaultFromName,
      fromEmail: this.defaultFromEmail,
      replacements: {
        userName: data.userName,
        notificationTitle: data.notificationTitle,
        notificationContent: data.notificationContent,
        actionLink: data.actionLink || '',
        actionText: data.actionText || 'View Details',
      },
      campaign: 'notification',
    };

    await this.emailService.sendEmail(emailData);
  }
}
