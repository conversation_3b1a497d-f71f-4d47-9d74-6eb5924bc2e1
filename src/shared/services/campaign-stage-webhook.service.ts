import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminDB } from '../../config';
import {
  CampaignStageProgress,
  CampaignStageProgressDocument,
} from '../entities/camp/campaign-stage-progress.entity';
import {
  EMAIL_CAMPAIGN_STAGE,
  STAGE_STATUS,
  USER_CAMPAIGN_STATUS,
} from '../constants/camp.constant';
import { CampaignStageFlowService } from './campaign-stage-flow.service';

@Injectable()
export class CampaignStageWebhookService {
  private readonly logger = new Logger(CampaignStageWebhookService.name);

  constructor(
    @InjectModel(CampaignStageProgress.name, AdminDB)
    private readonly campaignStageProgressModel: Model<CampaignStageProgressDocument>,
    @Inject(forwardRef(() => CampaignStageFlowService))
    private readonly campaignStageFlowService: CampaignStageFlowService,
  ) {}

  /**
   * Handle email open event for campaign stage flow
   */
  async handleEmailOpen(
    campId: string,
    email: string,
    messageId: string,
  ): Promise<void> {
    try {
      const progress = await this.campaignStageProgressModel.findOne({
        campId,
        email,
        overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
      });

      if (!progress) {
        this.logger.warn(
          `❌ No ACTIVE campaign progress found for ${email} in camp ${campId}`,
        );
        return;
      }

      // Check if this is the first time this email is opened
      const isFirstOpen = !progress.openedEmailIds.includes(messageId);

      if (!isFirstOpen) {
        // Email already opened before, don't process again
        this.logger.debug(
          `📧 Email ${messageId} already opened by ${email} in camp ${campId} - skipping duplicate open`,
        );
        return;
      }

      // Add to opened emails (first time open)
      progress.openedEmailIds.push(messageId);

      this.logger.log(
        `📧 First open detected for ${email} in camp ${campId}, stage: ${progress.currentStage}, messageId: ${messageId}`,
      );

      // Handle stage-specific logic (DO NOT force change stage here)
      await this.handleStageSpecificOpen(progress, messageId);

      progress.updatedAt = new Date();
      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to handle email open:`, error);
    }
  }

  /**
   * Handle email click event for campaign stage flow
   */
  async handleEmailClick(
    campId: string,
    email: string,
    messageId: string,
    clickedUrl: string,
  ): Promise<void> {
    try {
      const progress = await this.campaignStageProgressModel.findOne({
        campId,
        email,
        overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
      });

      if (!progress) {
        return;
      }

      // Add to clicked emails if not already there
      if (!progress.clickedEmailIds.includes(messageId)) {
        progress.clickedEmailIds.push(messageId);
      }

      // Also mark as opened if not already
      if (!progress.openedEmailIds.includes(messageId)) {
        progress.openedEmailIds.push(messageId);
      }

      // Handle stage-specific logic (same as open for most stages)
      await this.handleStageSpecificOpen(progress, messageId);

      progress.updatedAt = new Date();
      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to handle email click:`, error);
    }
  }

  /**
   * Handle stage-specific open logic
   * SIMPLIFIED: Any open triggers Conversion email immediately
   */
  private async handleStageSpecificOpen(
    progress: CampaignStageProgressDocument,
    messageId: string,
  ): Promise<void> {
    this.logger.log(
      `🎯 Email opened by ${progress.email}, sending Conversion email IMMEDIATELY, messageId: ${messageId}`,
    );

    // SIMPLIFIED LOGIC: Any email open triggers immediate Conversion email
    try {
      // Always send Conversion email regardless of current stage
      await this.sendConversionEmailImmediately(progress, messageId);
    } catch (error) {
      this.logger.error(
        `❌ Failed to send immediate Conversion email for ${progress.email}:`,
        error,
      );
    }

    // COMMENTED OUT: Complex stage-based logic
    /*
    switch (progress.currentStage) {
      case EMAIL_CAMPAIGN_STAGE.INITIATION:
        // Initiation stage: opens don't affect flow, continue as planned
        this.logger.log(
          `📍 Initiation stage - open recorded but no stage change`,
        );
        break;

      case EMAIL_CAMPAIGN_STAGE.ENGAGEMENT:
        // Engagement stage: any open triggers immediate move to Conversion
        if (!progress.hasEngagementOpen) {
          progress.hasEngagementOpen = true;
          this.logger.log(
            `🚀 Engagement stage - first open detected, moving to Conversion and sending first email IMMEDIATELY`,
          );

          // Move to Conversion stage and send first email immediately
          await this.moveToConversionStageAndSendEmail(progress);
        } else {
          this.logger.log(`📍 Engagement stage - open already detected before`);
        }
        break;

      case EMAIL_CAMPAIGN_STAGE.CONVERSION:
        // Conversion stage: track last opened email for sequential logic
        progress.lastOpenedEmailInConversion = messageId;

        this.logger.log(
          `🎯 Conversion stage - email opened, sending next email IMMEDIATELY`,
        );
        // Send next Conversion email immediately
        await this.sendNextConversionEmail(progress);
        break;

      case EMAIL_CAMPAIGN_STAGE.RETENTION:
        // Retention stage: opens don't affect flow
        this.logger.log(
          `📍 Retention stage - open recorded but no flow change`,
        );
        break;

      default:
        this.logger.warn(`❓ Unknown campaign stage: ${progress.currentStage}`);
    }
    */
  }

  /**
   * Get campaign stage progress for a user
   */
  async getCampaignProgress(
    campId: string,
    email: string,
  ): Promise<CampaignStageProgressDocument | null> {
    try {
      return await this.campaignStageProgressModel.findOne({
        campId,
        email,
      });
    } catch (error) {
      this.logger.error(`Failed to get campaign progress:`, error);
      return null;
    }
  }

  /**
   * Get all campaign progresses for a camp
   */
  async getCampaignProgresses(
    campId: string,
    stage?: EMAIL_CAMPAIGN_STAGE,
    status?: USER_CAMPAIGN_STATUS,
  ): Promise<CampaignStageProgressDocument[]> {
    try {
      const query: any = { campId };

      if (stage) {
        query.currentStage = stage;
      }

      if (status) {
        query.overallStatus = status;
      }

      return await this.campaignStageProgressModel.find(query);
    } catch (error) {
      this.logger.error(`Failed to get campaign progresses:`, error);
      return [];
    }
  }

  /**
   * Pause campaign for a user
   */
  async pauseCampaign(campId: string, email: string): Promise<boolean> {
    try {
      const result = await this.campaignStageProgressModel.updateOne(
        { campId, email },
        {
          overallStatus: USER_CAMPAIGN_STATUS.PAUSED,
          updatedAt: new Date(),
        },
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error(`Failed to pause campaign:`, error);
      return false;
    }
  }

  /**
   * Resume campaign for a user
   */
  async resumeCampaign(campId: string, email: string): Promise<boolean> {
    try {
      const result = await this.campaignStageProgressModel.updateOne(
        { campId, email, overallStatus: USER_CAMPAIGN_STATUS.PAUSED },
        {
          overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
          nextExecutionTime: new Date(), // Resume immediately
          updatedAt: new Date(),
        },
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error(`Failed to resume campaign:`, error);
      return false;
    }
  }

  /**
   * Stop campaign for a user
   */
  async stopCampaign(campId: string, email: string): Promise<boolean> {
    try {
      const result = await this.campaignStageProgressModel.updateOne(
        { campId, email },
        {
          overallStatus: USER_CAMPAIGN_STATUS.STOPPED,
          updatedAt: new Date(),
        },
      );

      return result.modifiedCount > 0;
    } catch (error) {
      this.logger.error(`Failed to stop campaign:`, error);
      return false;
    }
  }

  /**
   * Pause all campaigns for a camp
   */
  async pauseAllCampaigns(
    campId: string,
  ): Promise<{ success: boolean; message: string; executionsPaused: number }> {
    try {
      const result = await this.campaignStageProgressModel.updateMany(
        { campId, overallStatus: USER_CAMPAIGN_STATUS.ACTIVE },
        {
          overallStatus: USER_CAMPAIGN_STATUS.PAUSED,
          updatedAt: new Date(),
        },
      );

      return {
        success: true,
        message: `Paused ${result.modifiedCount} campaign progresses`,
        executionsPaused: result.modifiedCount || 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to pause all campaigns for camp ${campId}:`,
        error,
      );
      throw new Error(`Failed to pause campaigns: ${error.message}`);
    }
  }

  /**
   * Resume all campaigns for a camp
   */
  async resumeAllCampaigns(
    campId: string,
  ): Promise<{ success: boolean; message: string; executionsResumed: number }> {
    try {
      const result = await this.campaignStageProgressModel.updateMany(
        { campId, overallStatus: USER_CAMPAIGN_STATUS.PAUSED },
        {
          overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
          nextExecutionTime: new Date(), // Resume immediately
          updatedAt: new Date(),
        },
      );

      return {
        success: true,
        message: `Resumed ${result.modifiedCount} campaign progresses`,
        executionsResumed: result.modifiedCount || 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to resume all campaigns for camp ${campId}:`,
        error,
      );
      throw new Error(`Failed to resume campaigns: ${error.message}`);
    }
  }

  /**
   * Cancel all campaigns for a camp
   */
  async cancelAllCampaigns(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsCancelled: number;
  }> {
    try {
      const result = await this.campaignStageProgressModel.updateMany(
        { campId },
        {
          overallStatus: USER_CAMPAIGN_STATUS.STOPPED,
          updatedAt: new Date(),
        },
      );

      return {
        success: true,
        message: `Cancelled ${result.modifiedCount} campaign progresses`,
        executionsCancelled: result.modifiedCount || 0,
      };
    } catch (error) {
      this.logger.error(
        `Failed to cancel all campaigns for camp ${campId}:`,
        error,
      );
      throw new Error(`Failed to cancel campaigns: ${error.message}`);
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campId: string): Promise<any> {
    try {
      const stats = await this.campaignStageProgressModel.aggregate([
        { $match: { campId } },
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            activeUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.ACTIVE] },
                  1,
                  0,
                ],
              },
            },
            completedUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.COMPLETED] },
                  1,
                  0,
                ],
              },
            },
            stoppedUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.STOPPED] },
                  1,
                  0,
                ],
              },
            },
            pausedUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.PAUSED] },
                  1,
                  0,
                ],
              },
            },
            initiationUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$currentStage', EMAIL_CAMPAIGN_STAGE.INITIATION] },
                  1,
                  0,
                ],
              },
            },
            engagementUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$currentStage', EMAIL_CAMPAIGN_STAGE.ENGAGEMENT] },
                  1,
                  0,
                ],
              },
            },
            conversionUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$currentStage', EMAIL_CAMPAIGN_STAGE.CONVERSION] },
                  1,
                  0,
                ],
              },
            },
            retentionUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$currentStage', EMAIL_CAMPAIGN_STAGE.RETENTION] },
                  1,
                  0,
                ],
              },
            },
          },
        },
      ]);

      return (
        stats[0] || {
          totalUsers: 0,
          activeUsers: 0,
          completedUsers: 0,
          stoppedUsers: 0,
          pausedUsers: 0,
          initiationUsers: 0,
          engagementUsers: 0,
          conversionUsers: 0,
          retentionUsers: 0,
        }
      );
    } catch (error) {
      this.logger.error(`Failed to get campaign stats:`, error);
      return null;
    }
  }

  /**
   * Clear all campaign stage progresses
   */
  async clearAllProgresses(): Promise<{ cleared: number }> {
    try {
      const result = await this.campaignStageProgressModel.deleteMany({});
      return { cleared: result.deletedCount || 0 };
    } catch (error) {
      this.logger.error('Failed to clear all progresses:', error);
      throw new Error(`Failed to clear all progresses: ${error.message}`);
    }
  }

  /**
   * Clear campaign stage progresses for a specific camp
   */
  async clearCampProgresses(campId: string): Promise<{ cleared: number }> {
    try {
      const result = await this.campaignStageProgressModel.deleteMany({
        campId: campId,
      });
      return { cleared: result.deletedCount || 0 };
    } catch (error) {
      this.logger.error(
        `Failed to clear progresses for camp ${campId}:`,
        error,
      );
      throw new Error(`Failed to clear camp progresses: ${error.message}`);
    }
  }

  /**
   * Reset campaign stage progress for a specific user
   */
  async resetUserProgress(
    campId: string,
    email: string,
  ): Promise<{ success: boolean }> {
    try {
      const result = await this.campaignStageProgressModel.deleteOne({
        campId: campId,
        email: email,
      });

      return { success: result.deletedCount > 0 };
    } catch (error) {
      throw new Error(`Failed to reset user progress: ${error.message}`);
    }
  }

  /**
   * Reset user to Conversion stage for testing
   */
  async resetUserToConversionStage(
    campId: string,
    email: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const progress = await this.campaignStageProgressModel.findOne({
        campId,
        email,
      });

      if (!progress) {
        return {
          success: false,
          message: `No progress found for user ${email} in camp ${campId}`,
        };
      }

      // Reset to Conversion stage
      progress.currentStage = EMAIL_CAMPAIGN_STAGE.CONVERSION;
      progress.conversionStatus = STAGE_STATUS.IN_PROGRESS;
      progress.conversionStartedAt = new Date();
      progress.currentEmailIndex = 0;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.openedEmailIds = []; // Clear opened emails
      progress.sentEmailIds = []; // Clear sent emails
      progress.updatedAt = new Date();

      await progress.save();

      return {
        success: true,
        message: `User ${email} reset to Conversion stage successfully`,
      };
    } catch (error) {
      this.logger.error(
        `Failed to reset user to conversion stage: ${error.message}`,
      );
      return {
        success: false,
        message: `Failed to reset user: ${error.message}`,
      };
    }
  }

  /**
   * SIMPLIFIED: Send Conversion email immediately for any email open
   * No complex stage logic - just send Conversion email directly
   */
  private async sendConversionEmailImmediately(
    progress: CampaignStageProgressDocument,
    messageId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `🚀 SIMPLIFIED: Sending Conversion email immediately for user ${progress.email}, messageId: ${messageId}`,
      );

      // SIMPLIFIED: Force user to Conversion stage and send email immediately

      // Force update progress to Conversion stage if not already
      if (progress.currentStage !== EMAIL_CAMPAIGN_STAGE.CONVERSION) {
        this.logger.log(
          `� Moving user ${progress.email} from ${progress.currentStage} to CONVERSION stage`,
        );
        progress.currentStage = EMAIL_CAMPAIGN_STAGE.CONVERSION;
        progress.conversionStatus = STAGE_STATUS.IN_PROGRESS;
        progress.conversionStartedAt = new Date();
        progress.currentEmailIndex = 0; // Start with first Conversion email
      }

      // Track the opened email
      progress.lastOpenedEmailInConversion = messageId;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.updatedAt = new Date();
      await progress.save();

      this.logger.log(
        `📧 Triggering Conversion email flow for ${progress.email}`,
      );

      // Send Conversion email using the existing flow service
      await this.campaignStageFlowService.processConversionStage(progress);

      this.logger.log(
        `✅ SIMPLIFIED: Successfully sent Conversion email to ${progress.email}`,
      );
    } catch (error) {
      this.logger.error(
        `❌ SIMPLIFIED: Failed to send Conversion email for ${progress.email}:`,
        error,
      );
    }
  }

  // COMMENTED OUT: Complex stage-based methods
  /*
  private async moveToConversionStageAndSendEmail(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    // ... complex logic commented out
  }

  private async sendNextConversionEmail(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    // ... complex logic commented out
  }
  */
}
