import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { AdminDB } from '../../config';
import {
  EmailTemplate,
  EmailTemplateDocument,
  FLOW_TYPE,
  FLOW_ITEM_TYPE,
  PERIODIC_FREQUENCY,
  FlowItem,
  EmailFlowItem,
  DelayFlowItem,
} from '../entities/camp/email-template.entity';
import { Camp, CampDocument } from '../entities/camp/camp.entity';
import { CampUser, CampUserDocument } from '../entities/camp/camp-user.entity';
import {
  EMAIL_STATUS,
  CAMP_USER_STATUS,
  EMAIL_TYPE,
  CAMP_STATUS,
} from '../constants/camp.constant';
import { EmailService } from './email-service.service';

interface CampaignExecution {
  campId: string;
  campaignId: string;
  userId: string;
  flowType: FLOW_TYPE;
  currentStepIndex: number;
  nextExecutionTime: Date;
  status:
    | 'pending'
    | 'running'
    | 'completed'
    | 'failed'
    | 'paused'
    | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  pausedAt?: Date;
  resumedAt?: Date;
  cancelledAt?: Date;
  // Failure tracking fields
  failureCount?: number;
  lastFailureReason?: string;
  lastFailureAt?: Date;
  // Periodic execution specific fields
  periodicSendCount?: number;
  lastPeriodicSent?: Date;
}

@Injectable()
export class CampaignExecutionService {
  private readonly logger = new Logger(CampaignExecutionService.name);
  private readonly executions = new Map<string, CampaignExecution>();

  // Constants for failure handling
  private readonly MAX_FAILURE_COUNT = 3; // Maximum failures before marking as failed
  private readonly FAILURE_BACKOFF_MINUTES = 30; // Wait time after failure before retry
  private readonly MAX_CAMP_FAILURES = 10; // Maximum failures per camp before stopping all executions

  constructor(
    @InjectModel(EmailTemplate.name, AdminDB)
    private readonly emailTemplateModel: Model<EmailTemplateDocument>,
    @InjectModel(Camp.name, AdminDB)
    private readonly campModel: Model<CampDocument>,
    @InjectModel(CampUser.name, AdminDB)
    private readonly campUserModel: Model<CampUserDocument>,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Start campaign execution for a camp
   */
  async startCampaign(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsStarted: number;
  }> {
    try {
      // Get camp details first
      const camp = await this.campModel.findById(campId).lean();
      if (!camp) {
        throw new Error('Camp not found');
      }

      if (!camp.emailTemplate) {
        throw new Error('No email campaign associated with this camp');
      }

      // Get email campaign details
      const emailTemplate = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!emailTemplate) {
        throw new Error('Email campaign not found');
      }

      // Check camp status instead of email template status
      if (camp.status !== CAMP_STATUS.ACTIVE) {
        throw new Error('Campaign is not active');
      }

      // Get all active users in the camp
      const campUsers = await this.campUserModel
        .find({
          campId: campId,
          status: CAMP_USER_STATUS.ACTIVE,
        })
        .lean();

      if (campUsers.length === 0) {
        throw new Error('No active users found in camp');
      }

      let executionsStarted = 0;

      // Start execution for each user
      for (const user of campUsers) {
        const executionKey = `${campId}_${user.userId}_start`;

        // Check if execution already exists
        if (this.executions.has(executionKey)) {
          this.logger.warn(
            `Execution already exists for user ${user.userId} in camp ${campId}`,
          );
          continue;
        }

        // Create execution record
        const execution: CampaignExecution = {
          campId,
          campaignId: (emailTemplate as any)._id.toString(),
          userId: user.userId,
          flowType: FLOW_TYPE.START,
          currentStepIndex: 0,
          nextExecutionTime: new Date(), // Start immediately
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date(),
          failureCount: 0,
        };

        this.executions.set(executionKey, execution);
        executionsStarted++;
      }

      // Start periodic campaigns if enabled
      await this.startPeriodicCampaigns(campId, emailTemplate, campUsers);

      return {
        success: true,
        message: `Campaign started successfully for ${executionsStarted} users`,
        executionsStarted,
      };
    } catch (error) {
      this.logger.error(`Failed to start campaign for camp ${campId}:`, error);
      throw error;
    }
  }

  /**
   * Process pending executions (DISABLED - handled by CampaignStageFlowService)
   * This method is kept for compatibility but the cron job is disabled
   */
  // @Cron(CronExpression.EVERY_MINUTE) // DISABLED - CampaignStageFlowService handles this
  async processPendingExecutions(): Promise<void> {
    // This method is disabled to prevent duplicate email sending
    // CampaignStageFlowService.processCampaignStages() handles email processing
    return;
  }

  /**
   * Process a single execution
   */
  private async processExecution(execution: CampaignExecution): Promise<void> {
    try {
      execution.status = 'running';
      execution.updatedAt = new Date();

      // Get camp first, then email template
      const camp = await this.campModel.findById(execution.campId).lean();
      if (!camp || !camp.emailTemplate) {
        throw new Error('Camp or email template not found');
      }

      const emailTemplate = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!emailTemplate) {
        throw new Error('Email template not found');
      }

      // Get the appropriate flow
      const flow = this.getFlow(emailTemplate, execution.flowType);
      if (!flow || flow.length === 0) {
        this.logger.warn(
          `No flow found for type ${execution.flowType} in campaign ${execution.campaignId}`,
        );
        execution.status = 'completed';
        return;
      }

      // Check if we've reached the end of the flow
      if (execution.currentStepIndex >= flow.length) {
        execution.status = 'completed';
        return;
      }

      const currentStep = flow[execution.currentStepIndex];

      if (currentStep.type === FLOW_ITEM_TYPE.EMAIL) {
        try {
          await this.processEmailStep(execution, currentStep as EmailFlowItem);
          // Move to next step only if email was sent successfully
          execution.currentStepIndex++;
          execution.status = 'pending';
        } catch (error: any) {
          // Handle quota and rate limit errors
          if (error.reason === 'Daily quota exceeded') {
            this.logger.warn(
              `Daily quota exceeded, rescheduling execution for tomorrow`,
            );
            execution.nextExecutionTime = new Date(error.waitTime + Date.now());
            execution.status = 'pending';
            return; // Don't move to next step, retry this step later
          } else if (error.reason === 'Rate limit') {
            execution.nextExecutionTime = new Date(error.waitTime + Date.now());
            execution.status = 'pending';
            return; // Don't move to next step, retry this step later
          } else {
            // Handle other email failures with retry logic
            await this.handleEmailFailure(execution, error);
            return; // Exit early, status and next steps handled by handleEmailFailure
          }
        }
      } else if (currentStep.type === FLOW_ITEM_TYPE.DELAY) {
        await this.processDelayStep(execution, currentStep as DelayFlowItem);
        // Move to next step
        execution.currentStepIndex++;
        execution.status = 'pending';
      }

      execution.updatedAt = new Date();
    } catch (error) {
      this.logger.error(
        `Error processing execution for user ${execution.userId}:`,
        error,
      );
      execution.status = 'failed';
      execution.updatedAt = new Date();
      throw error;
    }
  }

  /**
   * Process email step
   */
  private async processEmailStep(
    execution: CampaignExecution,
    emailStep: EmailFlowItem,
  ): Promise<void> {
    try {
      // Get camp user details
      const campUser = await this.campUserModel.findOne({
        campId: execution.campId,
        userId: execution.userId,
      });

      if (!campUser) {
        throw new Error(`Camp user not found: ${execution.userId}`);
      }

      // Get camp details for sender info
      const camp = await this.campModel.findById(execution.campId).lean();
      if (!camp) {
        throw new Error(`Camp not found: ${execution.campId}`);
      }

      // Get email template for sender details (camp already retrieved above)
      const emailTemplate = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!emailTemplate) {
        throw new Error(`Email template not found: ${camp.emailTemplate}`);
      }

      // Get template requirements to understand what placeholders are expected
      const templateRequirements = emailTemplate.requirements || [];

      // Prepare email variables based on template requirements and user data
      const emailVariables = this.buildEmailVariables(
        campUser,
        emailTemplate,
        templateRequirements,
      );

      // Replace variables in subject and content
      const subject = this.replaceVariables(emailStep.subject, emailVariables);
      const content = this.replaceVariables(emailStep.content, emailVariables);

      // Validate that all required placeholders are replaced
      const unreplacedPlaceholders = this.findUnreplacedPlaceholders(content);
      if (unreplacedPlaceholders.length > 0) {
        this.logger.error(
          `Missing placeholders for ${campUser.email}: ${unreplacedPlaceholders.join(', ')}`,
        );

        // Log detailed debug information
        this.logUserDebugInfo(
          campUser,
          emailTemplate,
          emailVariables,
          unreplacedPlaceholders,
        );

        // Check if missing placeholders are in template requirements
        const missingRequiredPlaceholders = unreplacedPlaceholders.filter(
          (placeholder) =>
            templateRequirements.some(
              (req) =>
                req.toLowerCase() === placeholder.toLowerCase() ||
                req.toUpperCase() === placeholder.toUpperCase() ||
                req === placeholder,
            ),
        );

        if (missingRequiredPlaceholders.length > 0) {
          // These are required placeholders that are missing from user data
          throw new Error(
            `Missing required replacements for placeholders: ${missingRequiredPlaceholders.join(', ')}`,
          );
        } else {
          // These are placeholders not in requirements, just log warning
          this.logger.warn(
            `Unreplaced placeholders (not in requirements) for ${campUser.email}: ${unreplacedPlaceholders.join(', ')}`,
          );
        }
      }

      // Validate email data before sending
      if (!campUser.email || !subject || !content) {
        throw new Error(
          `Invalid email data: email=${campUser.email}, subject=${!!subject}, content=${!!content}`,
        );
      }

      // Send email with quota and rate limiting
      await this.emailService.sendEmail(
        {
          toEmail: campUser.email,
          subject: subject.trim(),
          htmlContent: content,
          fromName: (emailTemplate as any).senderName || 'Sender',
          fromEmail: (emailTemplate as any).sender,
          campaign: `camp_${execution.campId}`,
          webhookUrl: camp?.webhookUrl, // Include webhook URL from camp
          emailType: emailStep.flowType, // Include email type for webhook tracking
        },
        execution.campId,
      ); // Pass campId for quota tracking

      // Update email tracking
      await this.updateEmailTracking(
        campUser,
        emailStep.flowType,
        EMAIL_STATUS.SENT,
        new Date(),
      );
    } catch (error) {
      this.logger.error(
        `Failed to send email for step ${emailStep.id}:`,
        error,
      );

      // Update email tracking with failed status
      const campUser = await this.campUserModel.findOne({
        campId: execution.campId,
        userId: execution.userId,
      });

      if (campUser) {
        await this.updateEmailTracking(
          campUser,
          emailStep.flowType,
          EMAIL_STATUS.FAILED,
          new Date(),
        );
      }

      throw error;
    }
  }

  /**
   * Process delay step
   */
  private async processDelayStep(
    execution: CampaignExecution,
    delayStep: DelayFlowItem,
  ): Promise<void> {
    // Calculate next execution time
    const delayMs = delayStep.delay * 1000; // Convert seconds to milliseconds
    execution.nextExecutionTime = new Date(Date.now() + delayMs);
  }

  /**
   * Handle email failure with retry logic and concise logging
   */
  private async handleEmailFailure(
    execution: CampaignExecution,
    error: any,
  ): Promise<void> {
    // Initialize failure tracking if not exists
    if (!execution.failureCount) {
      execution.failureCount = 0;
    }

    execution.failureCount++;

    // Extract concise error message
    let errorMsg = 'Unknown error';
    if (error.response?.status) {
      const status = error.response.status;
      const data = error.response.data;
      errorMsg = `HTTP ${status}: ${data?.message || data?.error || 'Server error'}`;
    } else if (error.message) {
      errorMsg = error.message;
    }

    execution.lastFailureReason = errorMsg;
    execution.lastFailureAt = new Date();

    // Concise failure log
    this.logger.error(
      `Email failure ${execution.failureCount}/${this.MAX_FAILURE_COUNT} [${execution.campId}/${execution.userId}]: ${errorMsg}`,
    );

    if (execution.failureCount >= this.MAX_FAILURE_COUNT) {
      // Max failures reached, mark execution as failed and stop retrying
      execution.status = 'failed';
      this.logger.error(
        `Max failures reached [${execution.campId}/${execution.userId}] - stopping retries`,
      );

      // Remove from executions map to stop processing
      const executionKey = `${execution.campId}_${execution.userId}_${execution.flowType}`;
      this.executions.delete(executionKey);

      // Check if camp has too many failed executions
      await this.checkCampFailureThreshold(execution.campId);
    } else {
      // Schedule retry with backoff
      const backoffMs = this.FAILURE_BACKOFF_MINUTES * 60 * 1000; // Convert to milliseconds
      execution.nextExecutionTime = new Date(Date.now() + backoffMs);
      execution.status = 'pending';

      this.logger.warn(
        `Retry scheduled [${execution.campId}/${execution.userId}] in ${this.FAILURE_BACKOFF_MINUTES}min`,
      );
    }

    execution.updatedAt = new Date();
  }

  /**
   * Check if a camp has exceeded failure threshold and stop all executions if needed
   */
  private async checkCampFailureThreshold(campId: string): Promise<void> {
    const campExecutions = Array.from(this.executions.values()).filter(
      (exec) => exec.campId === campId,
    );

    const failedCount = campExecutions.filter(
      (exec) =>
        exec.status === 'failed' ||
        (exec.failureCount || 0) >= this.MAX_FAILURE_COUNT,
    ).length;

    if (failedCount >= this.MAX_CAMP_FAILURES) {
      this.logger.error(
        `Camp ${campId} has ${failedCount} failed executions, stopping all remaining executions`,
      );

      // Stop all remaining executions for this camp
      const executionsToStop = Array.from(this.executions.entries()).filter(
        ([, execution]) =>
          execution.campId === campId && execution.status !== 'completed',
      );

      executionsToStop.forEach(([key, execution]) => {
        execution.status = 'cancelled';
        execution.cancelledAt = new Date();
        execution.updatedAt = new Date();
        this.executions.delete(key);
      });

      // Update camp status to paused
      try {
        await this.campModel.findByIdAndUpdate(campId, {
          status: CAMP_STATUS.PAUSED,
          updatedAt: new Date(),
        });

        this.logger.warn(
          `Camp ${campId} paused due to excessive failures. Stopped ${executionsToStop.length} executions.`,
        );
      } catch (error) {
        this.logger.error(`Failed to update camp status for ${campId}:`, error);
      }
    }
  }

  /**
   * Get flow by type from email campaign
   */
  private getFlow(
    emailTemplate: EmailTemplateDocument,
    flowType: FLOW_TYPE,
  ): FlowItem[] {
    switch (flowType) {
      case FLOW_TYPE.START:
        return emailTemplate.startFlow || [];
      case FLOW_TYPE.REMIND:
        return emailTemplate.remindFlow || [];
      case FLOW_TYPE.END:
        return emailTemplate.endFlow || [];
      case FLOW_TYPE.PERIODIC:
        return emailTemplate.periodicFlow || [];
      default:
        return [];
    }
  }

  /**
   * Replace variables in text
   */
  private replaceVariables(
    text: string,
    variables: Record<string, string>,
  ): string {
    let result = text;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, value || '');
    });

    return result;
  }

  /**
   * Find unreplaced placeholders in text
   */
  private findUnreplacedPlaceholders(text: string): string[] {
    const placeholderRegex = /{{\\s*([^}]+)\\s*}}/g;
    const matches: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = placeholderRegex.exec(text)) !== null) {
      matches.push(match[1].trim());
    }

    return matches;
  }

  /**
   * Build email variables based on template requirements and user data
   */
  private buildEmailVariables(
    campUser: CampUserDocument,
    emailTemplate: any,
    templateRequirements: string[],
  ): Record<string, string> {
    const variables: Record<string, string> = {};

    // Standard mappings from user data
    const userFullName =
      `${campUser.firstName || ''} ${campUser.lastName || ''}`.trim();
    const userName = userFullName || campUser.email || 'User';

    // Base variables always available
    variables.email = campUser.email || '';
    variables.firstName = campUser.firstName || 'User';
    variables.lastName = campUser.lastName || '';
    variables.userName = userName;
    variables.companyName = emailTemplate.brand || 'Company';

    // Add all metadata as variables first (since we store all CSV columns in metadata)
    if (campUser.metadata) {
      Object.entries(campUser.metadata).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          variables[key] = String(value);
        }
      });
    }

    // Process template requirements dynamically (like other services do)
    templateRequirements.forEach((requirement) => {
      const reqKey = requirement.trim();

      // Try to find value in multiple ways (similar to camp.service.ts logic)
      let value = '';

      // 1. Direct match from metadata
      if (campUser.metadata?.[reqKey]) {
        value = String(campUser.metadata[reqKey]);
      }
      // 2. Case-insensitive match from metadata
      else if (campUser.metadata?.[reqKey.toLowerCase()]) {
        value = String(campUser.metadata[reqKey.toLowerCase()]);
      } else if (campUser.metadata?.[reqKey.toUpperCase()]) {
        value = String(campUser.metadata[reqKey.toUpperCase()]);
      }
      // 3. Special mappings for common fields
      else if (reqKey.toUpperCase() === 'EMAIL') {
        value = campUser.email || '';
      } else if (
        reqKey.toUpperCase() === 'NAME' ||
        reqKey.toUpperCase() === 'FULLNAME'
      ) {
        value = campUser.metadata?.fullname || userFullName || 'User';
      } else if (reqKey.toUpperCase() === 'FIRSTNAME') {
        value = campUser.firstName || 'User';
      } else if (reqKey.toUpperCase() === 'LASTNAME') {
        value = campUser.lastName || '';
      } else {
        // Fallback: provide meaningful default values instead of requirement name
        value = 'N/A';
      }

      // Add variable in multiple case variations for maximum compatibility
      variables[reqKey] = value;
      variables[reqKey.toLowerCase()] = value;
      variables[reqKey.toUpperCase()] = value;
      variables[
        reqKey.charAt(0).toUpperCase() + reqKey.slice(1).toLowerCase()
      ] = value;

      // Special case for Position/JOB variations
      if (
        reqKey.toLowerCase() === 'position' ||
        reqKey.toLowerCase() === 'job'
      ) {
        variables.Position = value;
        variables.position = value;
        variables.POSITION = value;
        variables.JOB = value;
        variables.job = value;
      }
    });

    return variables;
  }

  /**
   * Log detailed user information for debugging placeholder issues
   */
  private logUserDebugInfo(
    campUser: CampUserDocument,
    emailTemplate: any,
    emailVariables: Record<string, string>,
    unreplacedPlaceholders: string[],
  ): void {
    // Debug logging removed for production
  }

  /**
   * Update email tracking for camp user
   */
  private async updateEmailTracking(
    campUser: CampUserDocument,
    flowType: FLOW_TYPE,
    status: EMAIL_STATUS,
    timestamp: Date,
  ): Promise<void> {
    // Map flow type to email type
    const emailType = this.mapFlowTypeToEmailType(flowType);

    // Find or create email tracking
    let emailTrack = campUser.emailTracking.find(
      (track) => track.emailType === emailType,
    );

    if (!emailTrack) {
      emailTrack = {
        emailType,
        status,
        openCount: 0,
        clickCount: 0,
      };
      campUser.emailTracking.push(emailTrack);
    } else {
      emailTrack.status = status;
    }

    // Update timestamps based on status
    switch (status) {
      case EMAIL_STATUS.SENT:
        emailTrack.sentAt = timestamp;
        campUser.lastEmailSentAt = timestamp;
        break;
      case EMAIL_STATUS.DELIVERED:
        emailTrack.deliveredAt = timestamp;
        break;
      case EMAIL_STATUS.FAILED:
        emailTrack.failedAt = timestamp;
        break;
      case EMAIL_STATUS.BOUNCED:
        emailTrack.bouncedAt = timestamp;
        break;
    }

    campUser.updatedAt = new Date();
    await campUser.save();
  }

  /**
   * Map flow type to email type
   */
  private mapFlowTypeToEmailType(flowType: FLOW_TYPE): EMAIL_TYPE {
    switch (flowType) {
      case FLOW_TYPE.START:
        return EMAIL_TYPE.START;
      case FLOW_TYPE.END:
        return EMAIL_TYPE.END;
      case FLOW_TYPE.REMIND:
      case FLOW_TYPE.PERIODIC:
      default:
        return EMAIL_TYPE.OTHER;
    }
  }

  /**
   * Get execution status for a camp
   */
  async getCampaignExecutionStatus(campId: string): Promise<{
    totalExecutions: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    paused: number;
    cancelled: number;
  }> {
    const executions = Array.from(this.executions.values()).filter(
      (execution) => execution.campId === campId,
    );

    const status = {
      totalExecutions: executions.length,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      paused: 0,
      cancelled: 0,
    };

    executions.forEach((execution) => {
      if (status.hasOwnProperty(execution.status)) {
        status[execution.status]++;
      }
    });

    return status;
  }

  /**
   * Stop campaign execution for a camp (deprecated - use pauseCampaign instead)
   */
  async stopCampaign(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsStopped: number;
  }> {
    const executionsToStop = Array.from(this.executions.entries()).filter(
      ([, execution]) =>
        execution.campId === campId && execution.status !== 'completed',
    );

    let executionsStopped = 0;

    executionsToStop.forEach(([key, execution]) => {
      execution.status = 'completed';
      execution.updatedAt = new Date();
      this.executions.delete(key);
      executionsStopped++;
    });

    return {
      success: true,
      message: `Campaign stopped successfully. ${executionsStopped} executions stopped.`,
      executionsStopped,
    };
  }

  /**
   * Pause campaign execution for a camp (temporary stop)
   */
  async pauseCampaign(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsPaused: number;
  }> {
    try {
      // Get all executions for this camp
      const executionsToPause = Array.from(this.executions.entries()).filter(
        ([, execution]) => execution.campId === campId,
      );

      let executionsPaused = 0;

      executionsToPause.forEach(([, execution]) => {
        if (execution.status === 'pending' || execution.status === 'running') {
          execution.status = 'paused';
          execution.pausedAt = new Date();
          execution.updatedAt = new Date();
          executionsPaused++;
        }
      });

      // Update camp status to paused
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.PAUSED,
        updatedAt: new Date(),
      });

      return {
        success: true,
        message: `Campaign paused successfully. ${executionsPaused} executions paused.`,
        executionsPaused,
      };
    } catch (error) {
      this.logger.error(`Failed to pause campaign for camp ${campId}:`, error);
      throw error;
    }
  }

  /**
   * Resume campaign execution for a camp
   */
  async resumeCampaign(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsResumed: number;
  }> {
    try {
      // Get all executions for this camp
      const executionsToResume = Array.from(this.executions.entries()).filter(
        ([, execution]) => execution.campId === campId,
      );

      let executionsResumed = 0;

      executionsToResume.forEach(([, execution]) => {
        if (execution.status === 'paused') {
          execution.status = 'pending';
          execution.resumedAt = new Date();
          execution.updatedAt = new Date();
          // Reset next execution time to now for immediate processing
          execution.nextExecutionTime = new Date();
          // Reset failure count when resuming
          execution.failureCount = 0;
          execution.lastFailureReason = undefined;
          execution.lastFailureAt = undefined;
          executionsResumed++;
        }
      });

      // Update camp status to active
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.ACTIVE,
        updatedAt: new Date(),
      });

      return {
        success: true,
        message: `Campaign resumed successfully. ${executionsResumed} executions resumed.`,
        executionsResumed,
      };
    } catch (error) {
      this.logger.error(`Failed to resume campaign for camp ${campId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel campaign execution for a camp (permanent stop)
   */
  async cancelCampaign(campId: string): Promise<{
    success: boolean;
    message: string;
    executionsCancelled: number;
  }> {
    try {
      // Get all executions for this camp
      const executionsToCancel = Array.from(this.executions.entries()).filter(
        ([, execution]) => execution.campId === campId,
      );

      let executionsCancelled = 0;

      executionsToCancel.forEach(([key, execution]) => {
        execution.status = 'cancelled';
        execution.cancelledAt = new Date();
        execution.updatedAt = new Date();
        executionsCancelled++;
        // Remove from executions map
        this.executions.delete(key);
      });

      // Update camp status to cancelled
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.CANCELLED,
        updatedAt: new Date(),
      });

      return {
        success: true,
        message: `Campaign cancelled successfully. ${executionsCancelled} executions cancelled.`,
        executionsCancelled,
      };
    } catch (error) {
      this.logger.error(`Failed to cancel campaign for camp ${campId}:`, error);
      throw error;
    }
  }

  /**
   * Start periodic campaigns for users
   */
  private async startPeriodicCampaigns(
    campId: string,
    emailTemplate: any,
    campUsers: any[],
  ): Promise<void> {
    const periodicConfig = emailTemplate.periodicConfig;

    if (!periodicConfig?.enabled || !emailTemplate.periodicFlow?.length) {
      return;
    }

    const now = new Date();
    const startDate = periodicConfig.startDate
      ? new Date(periodicConfig.startDate)
      : now;

    // Only start if we're within the periodic campaign period
    if (startDate > now) {
      return;
    }

    for (const user of campUsers) {
      const executionKey = `${campId}_${user.userId}_periodic`;

      // Check if periodic execution already exists
      if (this.executions.has(executionKey)) {
        continue;
      }

      // Create periodic execution record
      const execution: CampaignExecution = {
        campId,
        campaignId: emailTemplate._id.toString(),
        userId: user.userId,
        flowType: FLOW_TYPE.PERIODIC,
        currentStepIndex: 0,
        nextExecutionTime: this.calculateInitialPeriodicTime(
          periodicConfig,
          now,
        ),
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        failureCount: 0,
        periodicSendCount: 0,
        lastPeriodicSent: undefined,
      };

      this.executions.set(executionKey, execution);
    }
  }

  /**
   * Calculate next periodic execution time based on frequency
   * @param periodicConfig - Configuration containing frequency and timing settings
   * @param fromDate - Base date to calculate from
   * @returns Next execution date
   */
  private calculateNextPeriodicTime(periodicConfig: any, fromDate: Date): Date {
    if (!periodicConfig || !periodicConfig.frequency) {
      throw new BadRequestException('Invalid periodic configuration provided');
    }

    const nextTime = new Date(fromDate);

    switch (periodicConfig.frequency) {
      case PERIODIC_FREQUENCY.WEEKLY:
        // Add 7 days for weekly frequency
        nextTime.setDate(nextTime.getDate() + 7);
        break;

      case PERIODIC_FREQUENCY.MONTHLY:
        // Add 1 month for monthly frequency
        // Handle month overflow (e.g., Jan 31 + 1 month = Feb 28/29)
        const currentMonth = nextTime.getMonth();
        const currentDate = nextTime.getDate();
        const currentYear = nextTime.getFullYear();

        nextTime.setMonth(currentMonth + 1);

        // Handle year overflow (December + 1 month = January next year)
        if (
          nextTime.getFullYear() !== currentYear &&
          nextTime.getMonth() !== 0
        ) {
          nextTime.setFullYear(currentYear + 1);
          nextTime.setMonth(0);
        }

        // If the day doesn't exist in the new month (e.g., Feb 31),
        // set to the last day of that month
        if (nextTime.getDate() !== currentDate) {
          nextTime.setDate(0); // Sets to last day of previous month
        }

        break;

      default:
        // Default fallback - add 1 day if frequency is not recognized
        this.logger.warn(
          `Unknown periodic frequency: ${periodicConfig.frequency}, defaulting to daily`,
        );
        nextTime.setDate(nextTime.getDate() + 1);
        break;
    }

    // Ensure the next time is always in the future
    const now = new Date();
    if (nextTime <= now) {
      this.logger.warn(
        `Calculated next time ${nextTime.toISOString()} is not in the future (current: ${now.toISOString()}), adjusting...`,
      );
      // Recursively calculate from current time to ensure future date
      return this.calculateNextPeriodicTime(periodicConfig, now);
    }

    // Validate against end date if specified
    if (periodicConfig.endDate) {
      const endDate = new Date(periodicConfig.endDate);
      if (nextTime > endDate) {
        // Return a date far in the future to indicate campaign should end
        return new Date('2099-12-31T23:59:59.999Z');
      }
    }

    return nextTime;
  }

  /**
   * Calculate initial periodic execution time when starting a campaign
   * @param periodicConfig - Configuration containing frequency and timing settings
   * @param currentTime - Current time when campaign starts
   * @returns Initial execution date
   */
  private calculateInitialPeriodicTime(
    periodicConfig: any,
    currentTime: Date,
  ): Date {
    if (!periodicConfig || !periodicConfig.frequency) {
      this.logger.warn(
        'Invalid periodic config for initial calculation, defaulting to immediate execution',
      );
      return new Date(currentTime.getTime() + 60000); // 1 minute from now
    }

    // If startDate is specified and in the future, use it
    if (periodicConfig.startDate) {
      const startDate = new Date(periodicConfig.startDate);
      if (startDate > currentTime) {
        return startDate;
      }
    }

    // Otherwise, calculate next period from current time
    return this.calculateNextPeriodicTime(periodicConfig, currentTime);
  }

  /**
   * Cleanup old failed executions (runs every hour)
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupFailedExecutions(): Promise<void> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    let cleanedCount = 0;

    for (const [key, execution] of this.executions.entries()) {
      // Remove executions that have been failed for more than 1 hour
      if (
        execution.status === 'failed' &&
        execution.lastFailureAt &&
        execution.lastFailureAt < oneHourAgo
      ) {
        this.executions.delete(key);
        cleanedCount++;
      }
      // Also remove completed executions older than 1 hour
      else if (
        execution.status === 'completed' &&
        execution.updatedAt < oneHourAgo
      ) {
        this.executions.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`🧹 Cleaned up ${cleanedCount} old executions`);
    }
  }

  /**
   * Process periodic executions (DISABLED - handled by CampaignStageFlowService)
   * This method is kept for compatibility but the cron job is disabled
   */
  // @Cron(CronExpression.EVERY_MINUTE) // DISABLED - CampaignStageFlowService handles this
  async processPeriodicExecutions(): Promise<void> {
    // This method is disabled to prevent duplicate email sending
    // CampaignStageFlowService.processCampaignStages() handles periodic processing
    return;
  }

  /**
   * Process a single periodic execution
   */
  private async processPeriodicExecution(
    execution: CampaignExecution,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(execution.campId).lean();
      if (!camp || !camp.emailTemplate) {
        throw new Error('Camp or email template not found');
      }

      const emailTemplate = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!emailTemplate) {
        throw new Error('Email template not found');
      }

      const periodicConfig = emailTemplate.periodicConfig;
      if (!periodicConfig?.enabled) {
        execution.status = 'completed';
        return;
      }

      const now = new Date();

      // Check if we've reached the end date
      if (periodicConfig.endDate && new Date(periodicConfig.endDate) < now) {
        execution.status = 'completed';
        return;
      }

      // Check if we've reached max send count
      if (
        periodicConfig.maxSendCount &&
        (execution.periodicSendCount || 0) >= periodicConfig.maxSendCount
      ) {
        execution.status = 'completed';
        return;
      }

      // Process the periodic flow
      const flow = emailTemplate.periodicFlow || [];
      if (flow.length === 0) {
        execution.status = 'completed';
        return;
      }

      // Reset to beginning of periodic flow
      execution.currentStepIndex = 0;
      execution.status = 'running';

      // Process each step in the periodic flow
      for (let i = 0; i < flow.length; i++) {
        const step = flow[i];

        if (step.type === FLOW_ITEM_TYPE.EMAIL) {
          try {
            await this.processEmailStep(execution, step as EmailFlowItem);
          } catch (error: any) {
            // Handle failures in periodic execution
            const executionKey = `${execution.campId}_${execution.userId}_${execution.flowType}`;
            await this.handleEmailFailure(execution, error);
            // If execution was removed from map (max failures reached), stop processing
            if (!this.executions.has(executionKey)) {
              return;
            }
          }
        } else if (step.type === FLOW_ITEM_TYPE.DELAY) {
          // For periodic flows, we process delays immediately
          // since the main delay is between periodic cycles
        }
      }

      // Update periodic execution tracking
      execution.periodicSendCount = (execution.periodicSendCount || 0) + 1;
      execution.lastPeriodicSent = now;
      execution.nextExecutionTime = this.calculateNextPeriodicTime(
        periodicConfig,
        now,
      );
      execution.status = 'pending';
      execution.updatedAt = new Date();
    } catch (error) {
      this.logger.error(
        `Error processing periodic execution for user ${execution.userId}:`,
        error,
      );
      execution.status = 'failed';
      execution.updatedAt = new Date();
      throw error;
    }
  }

  /**
   * Clear all campaign executions (for old execution system compatibility)
   */
  async clearAllExecutions(): Promise<{ cleared: number }> {
    try {
      // Since we're using the new CampaignStageProgress system,
      // this method is mainly for compatibility
      return { cleared: 0 };
    } catch (error) {
      this.logger.error('Failed to clear all executions:', error);
      throw new Error(`Failed to clear all executions: ${error.message}`);
    }
  }

  /**
   * Clear campaign executions for a specific camp (for old execution system compatibility)
   */
  async clearCampExecutions(campId: string): Promise<{ cleared: number }> {
    try {
      // Since we're using the new CampaignStageProgress system,
      // this method is mainly for compatibility
      return { cleared: 0 };
    } catch (error) {
      this.logger.error(
        `Failed to clear executions for camp ${campId}:`,
        error,
      );
      throw new Error(`Failed to clear camp executions: ${error.message}`);
    }
  }
}
