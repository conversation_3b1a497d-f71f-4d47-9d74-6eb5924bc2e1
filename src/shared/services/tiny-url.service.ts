import { Injectable, Logger } from '@nestjs/common';

// TinyURL API Response Types (based on actual API response)
export interface TinyUrlStats {
  enabled: boolean;
  public: boolean;
}

export interface TinyUrlApiResponse {
  domain: string;
  alias: string;
  is_main: boolean;
  is_archived: boolean;
  is_terminated: boolean;
  is_deleted: boolean;
  no_affiliate: boolean;
  stats: TinyUrlStats;
  tags: string[];
  created_at: string; // ISO date string
  expires_at: string | null;
  description: string | null;
  tiny_url: string; // The actual short URL
  affiliates: boolean;
  read_only: boolean;
}

// Internal TinyUrl interface for our application
export interface TinyUrl {
  originalUrl: string; // The original long URL
  tinyUrl: string; // The shortened URL
  alias: string; // TinyURL alias
  domain: string; // TinyURL domain
  createdAt: string; // Creation timestamp
  expiresAt: string | null; // Expiration timestamp
  isActive: boolean; // Whether the URL is active
  stats?: TinyUrlStats; // URL statistics
}

// Service response wrapper
export interface TinyUrlResponse {
  success: boolean;
  data?: TinyUrl;
  error?: string;
  rawResponse?: TinyUrlApiResponse; // Include raw API response for debugging
}

@Injectable()
export class TinyUrlService {
  private readonly logger = new Logger(TinyUrlService.name);
  private readonly tinyUrlEndpoint = 'https://x54.run/tinyurl';

  /**
   * Create a tiny URL from a long URL
   */
  async createTinyUrl(originalUrl: string): Promise<TinyUrlResponse> {
    try {
      this.logger.log(`🔗 Creating tiny URL for: ${originalUrl}`);

      // Validate URL format
      if (!this.isValidUrl(originalUrl)) {
        this.logger.error(`❌ Invalid URL format: ${originalUrl}`);
        return {
          success: false,
          error: 'Invalid URL format',
        };
      }

      this.logger.log(`✅ URL validation passed`);
      this.logger.log(
        `🌐 Making request to TinyURL endpoint: ${this.tinyUrlEndpoint}`,
      );

      // Make API request to TinyURL service
      const requestBody = { url: `microsoft-edge://open?url=${originalUrl}` };
      this.logger.log(`📤 Request body: ${JSON.stringify(requestBody)}`);

      const response = await fetch(this.tinyUrlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      this.logger.log(
        `📥 Response status: ${response.status} ${response.statusText}`,
      );

      if (!response.ok) {
        const errorText = await response
          .text()
          .catch(() => 'Unable to read error response');
        this.logger.error(`❌ HTTP error response: ${errorText}`);
        throw new Error(
          `HTTP error! status: ${response.status}, body: ${errorText}`,
        );
      }

      const data = await response.json();
      this.logger.log(`📊 Response data: ${JSON.stringify(data, null, 2)}`);

      // Validate response structure
      if (!this.validateTinyUrlResponse(data)) {
        this.logger.error(
          `❌ TinyURL service returned invalid response structure`,
        );
        return {
          success: false,
          error: 'Invalid response structure from TinyURL service',
          rawResponse: data,
        };
      }

      // Check if URL is terminated, deleted, or archived
      if (data.is_terminated || data.is_deleted || data.is_archived) {
        this.logger.error(
          `❌ TinyURL is not active: terminated=${data.is_terminated}, deleted=${data.is_deleted}, archived=${data.is_archived}`,
        );
        return {
          success: false,
          error: 'TinyURL is not active (terminated, deleted, or archived)',
          rawResponse: data,
        };
      }

      // Parse response into our internal format
      const tinyUrl = this.parseTinyUrlResponse(originalUrl, data);

      this.logger.log(`✅ Tiny URL created successfully: ${tinyUrl.tinyUrl}`);

      return {
        success: true,
        data: tinyUrl,
        rawResponse: data,
      };
    } catch (error) {
      this.logger.error('💥 Tiny URL creation crashed:', error);
      return {
        success: false,
        error: `Failed to create tiny URL: ${error.message}`,
      };
    }
  }

  /**
   * Get information about an existing TinyURL
   */
  async getTinyUrlInfo(tinyUrl: string): Promise<TinyUrlResponse> {
    try {
      this.logger.log(`🔍 Getting info for TinyURL: ${tinyUrl}`);

      // Extract alias from TinyURL
      const alias = this.extractAliasFromUrl(tinyUrl);
      if (!alias) {
        return {
          success: false,
          error: 'Invalid TinyURL format - cannot extract alias',
        };
      }

      // Make API request to get URL info (if TinyURL provides such endpoint)
      // Note: This is a placeholder - actual TinyURL API might have different endpoints
      const response = await fetch(`${this.tinyUrlEndpoint}/info/${alias}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!this.validateTinyUrlResponse(data)) {
        return {
          success: false,
          error: 'Invalid response structure from TinyURL service',
          rawResponse: data,
        };
      }

      // Note: TinyURL API response doesn't include original_url
      // We'll use empty string as placeholder since we're getting info about existing URL
      const tinyUrlInfo = this.parseTinyUrlResponse('', data);

      return {
        success: true,
        data: tinyUrlInfo,
        rawResponse: data,
      };
    } catch (error) {
      this.logger.error('Failed to get TinyURL info:', error);
      return {
        success: false,
        error: `Failed to get TinyURL info: ${error.message}`,
      };
    }
  }

  /**
   * Create multiple tiny URLs in batch
   */
  async createBatchTinyUrls(urls: string[]): Promise<TinyUrlResponse[]> {
    const promises = urls.map((url) => this.createTinyUrl(url));
    const results = await Promise.allSettled(promises);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(
          `Failed to create tiny URL for ${urls[index]}:`,
          result.reason,
        );
        return {
          success: false,
          error: `Failed to create tiny URL: ${result.reason?.message || 'Unknown error'}`,
        };
      }
    });
  }

  /**
   * Create tiny URL with retry mechanism
   */
  async createTinyUrlWithRetry(
    originalUrl: string,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<TinyUrlResponse> {
    let lastError: string = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const result = await this.createTinyUrl(originalUrl);

      if (result.success) {
        return result;
      }

      lastError = result.error || 'Unknown error';

      if (attempt < maxRetries) {
        this.logger.warn(
          `Attempt ${attempt} failed, retrying in ${retryDelay}ms...`,
        );
        await this.delay(retryDelay);
        retryDelay *= 2; // Exponential backoff
      }
    }

    this.logger.error(
      `All ${maxRetries} attempts failed for URL: ${originalUrl}`,
    );
    return {
      success: false,
      error: `Failed after ${maxRetries} attempts: ${lastError}`,
    };
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Extract alias from TinyURL
   */
  private extractAliasFromUrl(tinyUrl: string): string | null {
    try {
      const url = new URL(tinyUrl);

      // Handle different TinyURL formats
      // https://tinyurl.com/alias
      // https://tinyurl.com/alias?param=value
      const pathname = url.pathname;
      const alias = pathname.substring(1); // Remove leading slash

      return alias || null;
    } catch {
      return null;
    }
  }

  /**
   * Delay utility for retry mechanism
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Validate TinyURL API response
   */
  private validateTinyUrlResponse(data: any): data is TinyUrlApiResponse {
    return (
      data &&
      typeof data.domain === 'string' &&
      typeof data.alias === 'string' &&
      typeof data.tiny_url === 'string' &&
      typeof data.is_main === 'boolean' &&
      typeof data.is_archived === 'boolean' &&
      typeof data.is_terminated === 'boolean' &&
      typeof data.is_deleted === 'boolean' &&
      data.stats &&
      typeof data.stats.enabled === 'boolean' &&
      typeof data.stats.public === 'boolean'
    );
  }

  /**
   * Parse and normalize TinyURL API response
   */
  private parseTinyUrlResponse(
    originalUrl: string,
    data: TinyUrlApiResponse,
  ): TinyUrl {
    return {
      originalUrl: originalUrl,
      tinyUrl: data.tiny_url,
      alias: data.alias,
      domain: data.domain,
      createdAt: data.created_at,
      expiresAt: data.expires_at,
      isActive: !data.is_terminated && !data.is_deleted && !data.is_archived,
      stats: data.stats,
    };
  }

  /**
   * Get service health status
   */
  async getServiceHealth(): Promise<{
    status: string;
    endpoint: string;
    lastTest?: {
      url: string;
      success: boolean;
      tinyUrl?: string;
      error?: string;
    };
  }> {
    try {
      const testUrl = 'https://example.com';
      const result = await this.createTinyUrl(testUrl);

      return {
        status: result.success ? 'healthy' : 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
        lastTest: {
          url: testUrl,
          success: result.success,
          tinyUrl: result.data?.tinyUrl,
          error: result.error,
        },
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
        lastTest: {
          url: 'https://example.com',
          success: false,
          error: error.message,
        },
      };
    }
  }
}
