import { Injectable, Logger } from '@nestjs/common';

export interface TinyUrl {
  url: string;
  tinyUrl: string;
  success: boolean;
  message?: string;
}

export interface TinyUrlResponse {
  success: boolean;
  data?: TinyUrl;
  error?: string;
}

@Injectable()
export class TinyUrlService {
  private readonly logger = new Logger(TinyUrlService.name);
  private readonly tinyUrlEndpoint = 'https://x54.run/tinyurl';

  /**
   * Create a tiny URL from a long URL
   */
  async createTinyUrl(originalUrl: string): Promise<TinyUrlResponse> {
    try {
      this.logger.log(`🔗 Creating tiny URL for: ${originalUrl}`);

      // Validate URL format
      if (!this.isValidUrl(originalUrl)) {
        this.logger.error(`❌ Invalid URL format: ${originalUrl}`);
        return {
          success: false,
          error: 'Invalid URL format',
        };
      }

      this.logger.log(`✅ URL validation passed`);
      this.logger.log(
        `🌐 Making request to TinyURL endpoint: ${this.tinyUrlEndpoint}`,
      );

      // Make API request to TinyURL service
      const requestBody = { url: originalUrl };
      this.logger.log(`📤 Request body: ${JSON.stringify(requestBody)}`);

      const response = await fetch(this.tinyUrlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      this.logger.log(
        `📥 Response status: ${response.status} ${response.statusText}`,
      );

      if (!response.ok) {
        const errorText = await response
          .text()
          .catch(() => 'Unable to read error response');
        this.logger.error(`❌ HTTP error response: ${errorText}`);
        throw new Error(
          `HTTP error! status: ${response.status}, body: ${errorText}`,
        );
      }

      const data = await response.json();
      this.logger.log(`📊 Response data: ${JSON.stringify(data, null, 2)}`);

      // Handle different response formats
      if (data.success === false) {
        this.logger.error(`❌ TinyURL service returned error: ${data.message}`);
        return {
          success: false,
          error: data.message || 'Failed to create tiny URL',
        };
      }

      const tinyUrl: TinyUrl = {
        url: originalUrl,
        tinyUrl: data.tinyUrl || data.shortUrl || data.url,
        success: true,
        message: data.message,
      };

      this.logger.log(`✅ Tiny URL created successfully: ${tinyUrl.tinyUrl}`);

      return {
        success: true,
        data: tinyUrl,
      };
    } catch (error) {
      this.logger.error('💥 Tiny URL creation crashed:', error);
      return {
        success: false,
        error: `Failed to create tiny URL: ${error.message}`,
      };
    }
  }

  /**
   * Create multiple tiny URLs in batch
   */
  async createBatchTinyUrls(urls: string[]): Promise<TinyUrlResponse[]> {
    const promises = urls.map((url) => this.createTinyUrl(url));
    const results = await Promise.allSettled(promises);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(
          `Failed to create tiny URL for ${urls[index]}:`,
          result.reason,
        );
        return {
          success: false,
          error: `Failed to create tiny URL: ${result.reason?.message || 'Unknown error'}`,
        };
      }
    });
  }

  /**
   * Create tiny URL with retry mechanism
   */
  async createTinyUrlWithRetry(
    originalUrl: string,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<TinyUrlResponse> {
    let lastError: string = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const result = await this.createTinyUrl(originalUrl);

      if (result.success) {
        return result;
      }

      lastError = result.error || 'Unknown error';

      if (attempt < maxRetries) {
        this.logger.warn(
          `Attempt ${attempt} failed, retrying in ${retryDelay}ms...`,
        );
        await this.delay(retryDelay);
        retryDelay *= 2; // Exponential backoff
      }
    }

    this.logger.error(
      `All ${maxRetries} attempts failed for URL: ${originalUrl}`,
    );
    return {
      success: false,
      error: `Failed after ${maxRetries} attempts: ${lastError}`,
    };
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Delay utility for retry mechanism
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get service health status
   */
  async getServiceHealth(): Promise<{ status: string; endpoint: string }> {
    try {
      const testUrl = 'https://example.com';
      const result = await this.createTinyUrl(testUrl);

      return {
        status: result.success ? 'healthy' : 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
      };
    }
  }
}
