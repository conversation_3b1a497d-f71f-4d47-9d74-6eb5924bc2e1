import { Injectable, Logger } from '@nestjs/common';

export interface TinyUrl {
  url: string;
  tinyUrl: string;
  success: boolean;
  message?: string;
}

export interface TinyUrlResponse {
  success: boolean;
  data?: TinyUrl;
  error?: string;
}

@Injectable()
export class TinyUrlService {
  private readonly logger = new Logger(TinyUrlService.name);
  private readonly tinyUrlEndpoint = 'https://x54.run/tinyurl';

  /**
   * Create a tiny URL from a long URL
   */
  async createTinyUrl(originalUrl: string): Promise<TinyUrlResponse> {
    try {
      // Validate URL format
      if (!this.isValidUrl(originalUrl)) {
        return {
          success: false,
          error: 'Invalid URL format',
        };
      }

      // Make API request to TinyURL service
      const response = await fetch(this.tinyUrlEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: originalUrl,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle different response formats
      if (data.success === false) {
        return {
          success: false,
          error: data.message || 'Failed to create tiny URL',
        };
      }

      const tinyUrl: TinyUrl = {
        url: originalUrl,
        tinyUrl: data.tinyUrl || data.shortUrl || data.url,
        success: true,
        message: data.message,
      };

      return {
        success: true,
        data: tinyUrl,
      };
    } catch (error) {
      this.logger.error('Failed to create tiny URL:', error);
      return {
        success: false,
        error: `Failed to create tiny URL: ${error.message}`,
      };
    }
  }

  /**
   * Create multiple tiny URLs in batch
   */
  async createBatchTinyUrls(urls: string[]): Promise<TinyUrlResponse[]> {
    const promises = urls.map((url) => this.createTinyUrl(url));
    const results = await Promise.allSettled(promises);

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        this.logger.error(
          `Failed to create tiny URL for ${urls[index]}:`,
          result.reason,
        );
        return {
          success: false,
          error: `Failed to create tiny URL: ${result.reason?.message || 'Unknown error'}`,
        };
      }
    });
  }

  /**
   * Create tiny URL with retry mechanism
   */
  async createTinyUrlWithRetry(
    originalUrl: string,
    maxRetries: number = 3,
    retryDelay: number = 1000,
  ): Promise<TinyUrlResponse> {
    let lastError: string = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const result = await this.createTinyUrl(originalUrl);

      if (result.success) {
        return result;
      }

      lastError = result.error || 'Unknown error';

      if (attempt < maxRetries) {
        this.logger.warn(
          `Attempt ${attempt} failed, retrying in ${retryDelay}ms...`,
        );
        await this.delay(retryDelay);
        retryDelay *= 2; // Exponential backoff
      }
    }

    this.logger.error(
      `All ${maxRetries} attempts failed for URL: ${originalUrl}`,
    );
    return {
      success: false,
      error: `Failed after ${maxRetries} attempts: ${lastError}`,
    };
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Delay utility for retry mechanism
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get service health status
   */
  async getServiceHealth(): Promise<{ status: string; endpoint: string }> {
    try {
      const testUrl = 'https://example.com';
      const result = await this.createTinyUrl(testUrl);

      return {
        status: result.success ? 'healthy' : 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        endpoint: this.tinyUrlEndpoint,
      };
    }
  }
}
