import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AdminDB } from '../../config';
import {
  EmailQuota,
  EmailQuotaDocument,
} from '../entities/email/email-quota.entity';

@Injectable()
export class EmailQuotaService {
  private readonly logger = new Logger(EmailQuotaService.name);
  private readonly DAILY_LIMIT = 50000; // AWS SES daily limit
  private readonly RATE_LIMIT_DELAY = 30000; // 30 seconds between emails per camp
  private readonly campLastSentMap = new Map<string, number>(); // campId -> timestamp

  constructor(
    @InjectModel(EmailQuota.name, AdminDB)
    private readonly emailQuotaModel: Model<EmailQuotaDocument>,
  ) {
    this.initializeQuotaTracking();
  }

  /**
   * Initialize quota tracking on service startup
   */
  private async initializeQuotaTracking(): Promise<void> {
    try {
      const today = this.getTodayString();
      let todayQuota = await this.emailQuotaModel.findOne({ date: today });

      if (!todayQuota) {
        todayQuota = await this.emailQuotaModel.findOneAndUpdate(
          { date: today },
          {
            $setOnInsert: {
              date: today,
              emailsSent: 0,
              dailyLimit: this.DAILY_LIMIT,
              isQuotaExceeded: false,
              lastResetAt: new Date(),
              createdAt: new Date(),
            },
            $set: {
              updatedAt: new Date(),
            },
          },
          { upsert: true, new: true },
        );
      }
    } catch (error) {
      this.logger.error('Failed to initialize email quota tracking:', error);
    }
  }

  /**
   * Check if we can send email (quota + rate limiting)
   */
  async canSendEmail(campId: string): Promise<{
    canSend: boolean;
    reason?: string;
    waitTime?: number;
  }> {
    try {
      // Check daily quota
      const quotaCheck = await this.checkDailyQuota();
      if (!quotaCheck.canSend) {
        return quotaCheck;
      }

      // Check rate limiting per camp
      const rateLimitCheck = this.checkCampRateLimit(campId);
      if (!rateLimitCheck.canSend) {
        return rateLimitCheck;
      }

      return { canSend: true };
    } catch (error) {
      this.logger.error('Error checking email send permission:', error);
      return { canSend: false, reason: 'System error' };
    }
  }

  /**
   * Check daily quota limit
   */
  private async checkDailyQuota(): Promise<{
    canSend: boolean;
    reason?: string;
    waitTime?: number;
  }> {
    const today = this.getTodayString();
    const todayQuota = await this.emailQuotaModel.findOne({ date: today });

    if (!todayQuota) {
      this.logger.warn('No quota record found for today, creating one');
      await this.emailQuotaModel.findOneAndUpdate(
        { date: today },
        {
          $setOnInsert: {
            date: today,
            emailsSent: 0,
            dailyLimit: this.DAILY_LIMIT,
            isQuotaExceeded: false,
            lastResetAt: new Date(),
            createdAt: new Date(),
          },
          $set: {
            updatedAt: new Date(),
          },
        },
        { upsert: true, new: true },
      );
      return { canSend: true };
    }

    if (todayQuota.emailsSent >= this.DAILY_LIMIT) {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      const waitTime = tomorrow.getTime() - Date.now();

      this.logger.warn(
        `Daily email quota exceeded: ${todayQuota.emailsSent}/${this.DAILY_LIMIT}`,
      );
      return {
        canSend: false,
        reason: 'Daily quota exceeded',
        waitTime,
      };
    }

    return { canSend: true };
  }

  /**
   * Check rate limiting per camp (30 seconds between emails)
   */
  private checkCampRateLimit(campId: string): {
    canSend: boolean;
    reason?: string;
    waitTime?: number;
  } {
    const lastSentTime = this.campLastSentMap.get(campId) || 0;
    const timeSinceLastSent = Date.now() - lastSentTime;

    if (timeSinceLastSent < this.RATE_LIMIT_DELAY) {
      const waitTime = this.RATE_LIMIT_DELAY - timeSinceLastSent;
      return {
        canSend: false,
        reason: `Rate limit: wait ${Math.ceil(waitTime / 1000)}s`,
        waitTime,
      };
    }

    return { canSend: true };
  }

  /**
   * Record that an email was sent (update quota and rate limit)
   */
  async recordEmailSent(campId: string): Promise<void> {
    try {
      const today = this.getTodayString();

      // Update daily quota
      await this.emailQuotaModel.findOneAndUpdate(
        { date: today },
        {
          $inc: { emailsSent: 1 },
          $set: { updatedAt: new Date() },
        },
        { upsert: true, new: true },
      );

      // Update rate limit tracking
      this.campLastSentMap.set(campId, Date.now());
    } catch (error) {
      this.logger.error('❌ Failed to record email sent:', error);
    }
  }

  /**
   * Get current quota status
   */
  async getQuotaStatus(): Promise<{
    date: string;
    emailsSent: number;
    dailyLimit: number;
    remaining: number;
    isQuotaExceeded: boolean;
    resetTime: Date;
  }> {
    const today = this.getTodayString();
    let todayQuota = await this.emailQuotaModel.findOne({ date: today });

    if (!todayQuota) {
      todayQuota = await this.emailQuotaModel.findOneAndUpdate(
        { date: today },
        {
          $setOnInsert: {
            date: today,
            emailsSent: 0,
            dailyLimit: this.DAILY_LIMIT,
            isQuotaExceeded: false,
            lastResetAt: new Date(),
            createdAt: new Date(),
          },
          $set: {
            updatedAt: new Date(),
          },
        },
        { upsert: true, new: true },
      );
    }

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    return {
      date: today,
      emailsSent: todayQuota!.emailsSent,
      dailyLimit: this.DAILY_LIMIT,
      remaining: Math.max(0, this.DAILY_LIMIT - todayQuota!.emailsSent),
      isQuotaExceeded: todayQuota!.emailsSent >= this.DAILY_LIMIT,
      resetTime: tomorrow,
    };
  }

  /**
   * Reset daily quota (runs at midnight)
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async resetDailyQuota(): Promise<void> {
    try {
      const today = this.getTodayString();

      await this.emailQuotaModel.findOneAndUpdate(
        { date: today },
        {
          $set: {
            emailsSent: 0,
            dailyLimit: this.DAILY_LIMIT,
            isQuotaExceeded: false,
            lastResetAt: new Date(),
            updatedAt: new Date(),
          },
          $setOnInsert: {
            date: today,
            createdAt: new Date(),
          },
        },
        { upsert: true, new: true },
      );

      // Clear rate limit cache
      this.campLastSentMap.clear();

      this.logger.log('✅ Daily email quota reset successfully');
    } catch (error) {
      this.logger.error('❌ Failed to reset daily quota:', error);
    }
  }

  /**
   * Get today's date string in YYYY-MM-DD format
   */
  private getTodayString(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get camp rate limit status
   */
  getCampRateLimitStatus(campId: string): {
    lastSentTime: number;
    canSendNow: boolean;
    nextAvailableTime: number;
  } {
    const lastSentTime = this.campLastSentMap.get(campId) || 0;
    const nextAvailableTime = lastSentTime + this.RATE_LIMIT_DELAY;
    const canSendNow = Date.now() >= nextAvailableTime;

    return {
      lastSentTime,
      canSendNow,
      nextAvailableTime,
    };
  }
}
