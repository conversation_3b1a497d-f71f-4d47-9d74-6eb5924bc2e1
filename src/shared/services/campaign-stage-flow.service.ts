import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { AdminDB } from '../../config';
import {
  CampaignStageProgress,
  CampaignStageProgressDocument,
} from '../entities/camp/campaign-stage-progress.entity';
import { CampUser, CampUserDocument } from '../entities/camp/camp-user.entity';
import {
  EmailTemplate,
  EmailTemplateDocument,
} from '../entities/camp/email-template.entity';
import {
  EMAIL_CAMPAIGN_STAGE,
  STAGE_STATUS,
  USER_CAMPAIGN_STATUS,
  CAMP_STATUS,
  CAMP_USER_STATUS,
  EMAIL_STATUS,
} from '../constants/camp.constant';
import { Camp, CampDocument } from '../entities/camp/camp.entity';
import { EmailService } from './email-service.service';
import { EmailQuotaService } from './email-quota.service';
import { EmailContentProcessorService } from './email-content-processor.service';

@Injectable()
export class CampaignStageFlowService {
  private readonly logger = new Logger(CampaignStageFlowService.name);

  constructor(
    @InjectModel(CampaignStageProgress.name, AdminDB)
    private readonly campaignStageProgressModel: Model<CampaignStageProgressDocument>,
    @InjectModel(CampUser.name, AdminDB)
    private readonly campUserModel: Model<CampUserDocument>,
    @InjectModel(EmailTemplate.name, AdminDB)
    private readonly emailTemplateModel: Model<EmailTemplateDocument>,
    @InjectModel(Camp.name, AdminDB)
    private readonly campModel: Model<CampDocument>,
    private readonly emailService: EmailService,
    private readonly emailQuotaService: EmailQuotaService,
    private readonly emailContentProcessorService: EmailContentProcessorService,
  ) {}

  /**
   * Initialize campaign stage progress for a user
   */
  async initializeCampaignProgress(
    campId: string,
    userId: string,
    email: string,
  ): Promise<CampaignStageProgressDocument> {
    try {
      // Check if progress already exists
      let progress = await this.campaignStageProgressModel.findOne({
        campId,
        userId,
      });

      if (progress) {
        return progress;
      }

      // Create new progress
      progress = await this.campaignStageProgressModel.create({
        campId,
        userId,
        email,
        overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
        currentStage: EMAIL_CAMPAIGN_STAGE.INITIATION,
        currentEmailIndex: 0,
        initiationStatus: STAGE_STATUS.PENDING,
        engagementStatus: STAGE_STATUS.PENDING,
        conversionStatus: STAGE_STATUS.PENDING,
        retentionStatus: STAGE_STATUS.PENDING,
        sentEmailIds: [],
        openedEmailIds: [],
        clickedEmailIds: [],
        hasEngagementOpen: false,
        retentionEmailsSent: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      return progress;
    } catch (error) {
      this.logger.error(`Failed to initialize campaign progress:`, error);
      throw error;
    }
  }

  /**
   * Start campaign for a user (begin Initiation stage)
   */
  async startCampaignForUser(
    campId: string,
    userId: string,
    email: string,
  ): Promise<void> {
    try {
      const progress = await this.initializeCampaignProgress(
        campId,
        userId,
        email,
      );

      // Start Initiation stage
      await this.startInitiationStage(progress);
    } catch (error) {
      this.logger.error(`Failed to start campaign for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Start Initiation stage
   */
  private async startInitiationStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.currentStage = EMAIL_CAMPAIGN_STAGE.INITIATION;
      progress.initiationStatus = STAGE_STATUS.IN_PROGRESS;
      progress.initiationStartedAt = new Date();
      progress.currentEmailIndex = 0;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to start Initiation stage:`, error);
      throw error;
    }
  }

  /**
   * Check for scheduled campaigns to start (runs every 5 minutes)
   */
  @Cron('*/5 * * * *') // Every 5 minutes
  async checkScheduledCampaigns(): Promise<void> {
    try {
      const now = new Date();

      // Find camps that should start now
      const scheduledCamps = await this.campModel
        .find({
          status: CAMP_STATUS.DRAFT, // Only draft camps can be auto-started
          startDate: { $lte: now }, // Start date has passed
          emailTemplate: { $exists: true, $ne: null }, // Has email template
        })
        .lean();

      if (scheduledCamps.length === 0) {
        return;
      }

      for (const camp of scheduledCamps) {
        try {
          // Check if campaign has users
          const campUsers = await this.campUserModel.find({
            campId: camp._id.toString(),
            status: CAMP_USER_STATUS.ACTIVE,
          });

          if (campUsers.length === 0) {
            this.logger.warn(`Skipping camp ${camp._id} - no active users`);
            continue;
          }

          // Auto-start the campaign for each user
          let successCount = 0;
          for (const user of campUsers) {
            try {
              await this.startCampaignForUser(
                camp._id.toString(),
                user.userId,
                user.email,
              );
              successCount++;
            } catch (error) {
              this.logger.error(
                `Failed to start campaign for user ${user.userId}:`,
                error,
              );
            }
          }

          // Update camp status to ACTIVE
          await this.campModel.findByIdAndUpdate(camp._id, {
            status: CAMP_STATUS.ACTIVE,
            updatedAt: new Date(),
            updatedBy: 'system', // System auto-start
          });
        } catch (error) {
          this.logger.error(
            `Failed to auto-start campaign for camp ${camp._id}:`,
            error,
          );

          // Update camp status to indicate failure
          await this.campModel.findByIdAndUpdate(camp._id, {
            status: CAMP_STATUS.CANCELLED,
            updatedAt: new Date(),
            updatedBy: 'system',
          });
        }
      }
    } catch (error) {
      this.logger.error('Error checking scheduled campaigns:', error);
    }
  }

  /**
   * Process campaign stages (runs every minute)
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async processCampaignStages(): Promise<void> {
    try {
      const now = new Date();

      // Find all active campaign progresses that need processing
      const activeProgresses = await this.campaignStageProgressModel
        .find({
          overallStatus: USER_CAMPAIGN_STATUS.ACTIVE,
          nextExecutionTime: { $lte: now },
        })
        .limit(100); // Process in batches

      for (const progress of activeProgresses) {
        try {
          await this.processSingleCampaignProgress(progress);
        } catch (error) {
          this.logger.error(
            `Failed to process campaign progress ${progress._id}:`,
            error,
          );

          // Update error info
          progress.lastError = error.message;
          progress.lastErrorAt = new Date();
          progress.updatedAt = new Date();
          await progress.save();
        }
      }
    } catch (error) {
      this.logger.error('Failed to process campaign stages:', error);
    }
  }

  /**
   * Process single campaign progress
   */
  private async processSingleCampaignProgress(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    // Check if camp is still active
    const camp = await this.campModel.findById(progress.campId).lean();
    if (!camp || camp.status !== CAMP_STATUS.ACTIVE) {
      return;
    }

    switch (progress.currentStage) {
      case EMAIL_CAMPAIGN_STAGE.INITIATION:
        await this.processInitiationStage(progress);
        break;
      case EMAIL_CAMPAIGN_STAGE.ENGAGEMENT:
        await this.processEngagementStage(progress);
        break;
      case EMAIL_CAMPAIGN_STAGE.CONVERSION:
        await this.processConversionStage(progress);
        break;
      case EMAIL_CAMPAIGN_STAGE.RETENTION:
        await this.processRetentionStage(progress);
        break;
      default:
        this.logger.warn(`Unknown campaign stage: ${progress.currentStage}`);
    }
  }

  /**
   * Process Initiation stage
   */
  private async processInitiationStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        return;
      }

      // Get email template
      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.startFlow) {
        this.logger.warn(`No start flow found for camp ${progress.campId}`);
        return;
      }

      const initiationEmails = template.startFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (progress.currentEmailIndex >= initiationEmails.length) {
        // All initiation emails sent, move to Engagement
        await this.completeInitiationStage(progress);
        return;
      }

      const currentEmail = initiationEmails[progress.currentEmailIndex];

      // Check quota and rate limits
      const canSend = await this.emailQuotaService.canSendEmail(
        progress.campId,
      );
      if (!canSend.canSend) {
        // Reschedule for later
        progress.nextExecutionTime = new Date(
          Date.now() + (canSend.waitTime || 60000),
        );
        progress.updatedAt = new Date();
        await progress.save();
        return;
      }

      // Send email
      const messageId = await this.sendStageEmail(progress, currentEmail);

      // Update progress
      progress.sentEmailIds.push(messageId);
      progress.currentEmailIndex++;
      progress.lastEmailSentAt = new Date(); // Track when this email was sent

      // Calculate next execution time based on email delay
      const delayMs = (currentEmail.delay || 0) * 1000; // Convert seconds to milliseconds
      progress.nextExecutionTime = new Date(Date.now() + delayMs);
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to process Initiation stage:`, error);
      throw error;
    }
  }

  /**
   * Complete Initiation stage and move to Engagement
   */
  private async completeInitiationStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.initiationStatus = STAGE_STATUS.COMPLETED;
      progress.initiationCompletedAt = new Date();

      // Move to Engagement stage
      await this.startEngagementStage(progress);
    } catch (error) {
      this.logger.error(`Failed to complete Initiation stage:`, error);
      throw error;
    }
  }

  /**
   * Start Engagement stage
   */
  private async startEngagementStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.skipEngagementStage(progress);
        return;
      }

      // Get email template
      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.remindFlow) {
        // No engagement emails, skip to Conversion or Retention
        await this.skipEngagementStage(progress);
        return;
      }

      const engagementEmails = template.remindFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (engagementEmails.length === 0) {
        await this.skipEngagementStage(progress);
        return;
      }

      progress.currentStage = EMAIL_CAMPAIGN_STAGE.ENGAGEMENT;
      progress.engagementStatus = STAGE_STATUS.IN_PROGRESS;
      progress.engagementStartedAt = new Date();
      progress.currentEmailIndex = 0;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to start Engagement stage:`, error);
      throw error;
    }
  }

  /**
   * Skip Engagement stage
   */
  private async skipEngagementStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.engagementStatus = STAGE_STATUS.SKIPPED;
      progress.engagementCompletedAt = new Date();

      // Move to Conversion stage
      await this.startConversionStage(progress);
    } catch (error) {
      this.logger.error(`Failed to skip Engagement stage:`, error);
      throw error;
    }
  }

  /**
   * Process Engagement stage
   */
  private async processEngagementStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Check if any email was opened in Engagement stage
      if (progress.hasEngagementOpen) {
        // Move to Conversion stage immediately
        await this.completeEngagementStage(progress);
        return;
      }

      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.completeEngagementStage(progress);
        return;
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.remindFlow) {
        await this.completeEngagementStage(progress);
        return;
      }

      const engagementEmails = template.remindFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (progress.currentEmailIndex >= engagementEmails.length) {
        // All engagement emails sent without opens, move to Retention
        await this.completeEngagementStageWithoutOpen(progress);
        return;
      }

      const currentEmail = engagementEmails[progress.currentEmailIndex];

      // Check quota and rate limits
      const canSend = await this.emailQuotaService.canSendEmail(
        progress.campId,
      );
      if (!canSend.canSend) {
        progress.nextExecutionTime = new Date(
          Date.now() + (canSend.waitTime || 60000),
        );
        progress.updatedAt = new Date();
        await progress.save();
        return;
      }

      // Send email
      const messageId = await this.sendStageEmail(progress, currentEmail);

      // Update progress
      progress.sentEmailIds.push(messageId);
      progress.currentEmailIndex++;
      progress.lastEmailSentAt = new Date(); // Track when this email was sent

      // Calculate next execution time
      const delayMs = (currentEmail.delay || 0) * 1000;
      progress.nextExecutionTime = new Date(Date.now() + delayMs);
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to process Engagement stage:`, error);
      throw error;
    }
  }

  /**
   * Complete Engagement stage (with open) and move to Conversion
   */
  private async completeEngagementStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.engagementStatus = STAGE_STATUS.COMPLETED;
      progress.engagementCompletedAt = new Date();

      // Move to Conversion stage
      await this.startConversionStage(progress);
    } catch (error) {
      this.logger.error(`Failed to complete Engagement stage:`, error);
      throw error;
    }
  }

  /**
   * Complete Engagement stage (without open) and move to Retention
   */
  private async completeEngagementStageWithoutOpen(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.engagementStatus = STAGE_STATUS.COMPLETED;
      progress.engagementCompletedAt = new Date();

      // Move to Retention stage (skip Conversion)
      await this.startRetentionStage(progress);
    } catch (error) {
      this.logger.error(
        `Failed to complete Engagement stage without open:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Start Conversion stage
   */
  private async startConversionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.skipConversionStage(progress);
        return;
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.endFlow) {
        // No conversion emails, skip to Retention
        await this.skipConversionStage(progress);
        return;
      }

      const conversionEmails = template.endFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (conversionEmails.length === 0) {
        await this.skipConversionStage(progress);
        return;
      }

      progress.currentStage = EMAIL_CAMPAIGN_STAGE.CONVERSION;
      progress.conversionStatus = STAGE_STATUS.IN_PROGRESS;
      progress.conversionStartedAt = new Date();
      progress.currentEmailIndex = 0;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to start Conversion stage:`, error);
      throw error;
    }
  }

  /**
   * Skip Conversion stage
   */
  private async skipConversionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.conversionStatus = STAGE_STATUS.SKIPPED;
      progress.conversionCompletedAt = new Date();

      // Move to Retention stage
      await this.startRetentionStage(progress);
    } catch (error) {
      this.logger.error(`Failed to skip Conversion stage:`, error);
      throw error;
    }
  }

  /**
   * Process Conversion stage (PUBLIC - can be called from webhook)
   */
  async processConversionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.completeConversionStage(progress);
        return;
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.endFlow) {
        await this.completeConversionStage(progress);
        return;
      }

      const conversionEmails = template.endFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (progress.currentEmailIndex >= conversionEmails.length) {
        // All conversion emails sent, move to Retention
        await this.completeConversionStage(progress);
        return;
      }

      const currentEmail = conversionEmails[progress.currentEmailIndex];

      // Check if previous email was opened (for Conversion stage logic)
      if (progress.currentEmailIndex > 0) {
        // Get the previous email ID based on current index
        const previousEmailIndex = progress.currentEmailIndex - 1;
        const previousEmailId = progress.sentEmailIds[previousEmailIndex];

        this.logger.log(
          `🔍 Conversion stage check: user ${progress.email}, currentIndex: ${progress.currentEmailIndex}, previousEmailId: ${previousEmailId}, openedEmails: [${progress.openedEmailIds.join(', ')}]`,
        );

        // Check if previous email was opened
        if (!progress.openedEmailIds.includes(previousEmailId)) {
          // Check if delay time has passed for previous email
          const previousEmailIndex = progress.currentEmailIndex - 1;
          const previousEmail = conversionEmails[previousEmailIndex];
          const previousEmailSentTime =
            progress.lastEmailSentAt ||
            progress.conversionStartedAt ||
            new Date();
          const delayMs = (previousEmail.delay || 0) * 1000;
          const shouldWaitUntil = new Date(
            previousEmailSentTime.getTime() + delayMs,
          );

          this.logger.log(
            `⏰ Previous email ${previousEmailId} not opened. Delay: ${delayMs}ms, shouldWaitUntil: ${shouldWaitUntil.toISOString()}, now: ${new Date().toISOString()}`,
          );

          if (new Date() < shouldWaitUntil) {
            // Still within delay period, wait longer
            this.logger.log(
              `⏳ Still within delay period, waiting until ${shouldWaitUntil.toISOString()}`,
            );
            progress.nextExecutionTime = shouldWaitUntil;
            progress.updatedAt = new Date();
            await progress.save();

            return;
          } else {
            // Delay time has passed and email not opened, stop Conversion stage
            this.logger.log(
              `🛑 Delay time passed and email not opened, stopping Conversion stage`,
            );
            await this.stopConversionStage(progress);
            return;
          }
        } else {
          this.logger.log(
            `✅ Previous email ${previousEmailId} was opened, proceeding to send next email`,
          );
        }
      } else {
        this.logger.log(
          `🚀 First email in Conversion stage, sending immediately`,
        );
      }

      // Check quota and rate limits
      const canSend = await this.emailQuotaService.canSendEmail(
        progress.campId,
      );
      if (!canSend.canSend) {
        progress.nextExecutionTime = new Date(
          Date.now() + (canSend.waitTime || 60000),
        );
        progress.updatedAt = new Date();
        await progress.save();
        return;
      }

      // Send email
      const messageId = await this.sendStageEmail(progress, currentEmail);

      // Update progress
      progress.sentEmailIds.push(messageId);
      progress.currentEmailIndex++;
      progress.lastEmailSentAt = new Date(); // Track when this email was sent

      // Calculate next execution time
      const delayMs = (currentEmail.delay || 0) * 1000;
      progress.nextExecutionTime = new Date(Date.now() + delayMs);
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to process Conversion stage:`, error);
      throw error;
    }
  }

  /**
   * Complete Conversion stage and move to Retention
   */
  private async completeConversionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.conversionStatus = STAGE_STATUS.COMPLETED;
      progress.conversionCompletedAt = new Date();

      // Move to Retention stage
      await this.startRetentionStage(progress);
    } catch (error) {
      this.logger.error(`Failed to complete Conversion stage:`, error);
      throw error;
    }
  }

  /**
   * Stop Conversion stage (due to no email opens) and move to Retention
   */
  private async stopConversionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.conversionStatus = STAGE_STATUS.STOPPED;
      progress.conversionCompletedAt = new Date();

      // Move to Retention stage
      await this.startRetentionStage(progress);
    } catch (error) {
      this.logger.error(`Failed to stop Conversion stage:`, error);
      throw error;
    }
  }

  /**
   * Start Retention stage
   */
  private async startRetentionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.completeCampaign(progress);
        return;
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.periodicFlow) {
        // No retention emails, complete campaign
        await this.completeCampaign(progress);
        return;
      }

      const retentionEmails = template.periodicFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (retentionEmails.length === 0) {
        await this.completeCampaign(progress);
        return;
      }

      progress.currentStage = EMAIL_CAMPAIGN_STAGE.RETENTION;
      progress.retentionStatus = STAGE_STATUS.IN_PROGRESS;
      progress.retentionStartedAt = new Date();
      progress.currentEmailIndex = 0;
      progress.nextExecutionTime = new Date(); // Send immediately
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to start Retention stage:`, error);
      throw error;
    }
  }

  /**
   * Process Retention stage
   */
  private async processRetentionStage(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      // Get camp first, then email template
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        this.logger.warn(
          `No camp or email template found for camp ${progress.campId}`,
        );
        await this.completeCampaign(progress);
        return;
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template || !template.periodicFlow) {
        await this.completeCampaign(progress);
        return;
      }

      const retentionEmails = template.periodicFlow.filter(
        (item: any) => item.type === 'email',
      );

      if (progress.currentEmailIndex >= retentionEmails.length) {
        // All retention emails sent, complete campaign
        await this.completeCampaign(progress);
        return;
      }

      const currentEmail = retentionEmails[progress.currentEmailIndex];

      // Check quota and rate limits
      const canSend = await this.emailQuotaService.canSendEmail(
        progress.campId,
      );
      if (!canSend.canSend) {
        progress.nextExecutionTime = new Date(
          Date.now() + (canSend.waitTime || 60000),
        );
        progress.updatedAt = new Date();
        await progress.save();
        return;
      }

      // Send email
      const messageId = await this.sendStageEmail(progress, currentEmail);

      // Update progress
      progress.sentEmailIds.push(messageId);
      progress.currentEmailIndex++;
      progress.retentionEmailsSent++;
      progress.lastRetentionEmailSent = new Date();
      progress.lastEmailSentAt = new Date(); // Track when this email was sent

      // Calculate next execution time based on periodic config
      const periodicConfig = template.periodicConfig;
      let delayMs = 7 * 24 * 60 * 60 * 1000; // Default: 1 week

      if (periodicConfig?.frequency === 'weekly') {
        delayMs = 7 * 24 * 60 * 60 * 1000;
      } else if (periodicConfig?.frequency === 'monthly') {
        delayMs = 30 * 24 * 60 * 60 * 1000;
      }

      progress.nextExecutionTime = new Date(Date.now() + delayMs);
      progress.updatedAt = new Date();

      await progress.save();
    } catch (error) {
      this.logger.error(`Failed to process Retention stage:`, error);
      throw error;
    }
  }

  /**
   * Complete campaign
   */
  private async completeCampaign(
    progress: CampaignStageProgressDocument,
  ): Promise<void> {
    try {
      progress.retentionStatus = STAGE_STATUS.COMPLETED;
      progress.retentionCompletedAt = new Date();
      progress.overallStatus = USER_CAMPAIGN_STATUS.COMPLETED;
      progress.updatedAt = new Date();

      await progress.save();

      // Check if all users in this camp have completed their campaigns
      await this.checkAndUpdateCampCompletionStatus(progress.campId);
    } catch (error) {
      this.logger.error(`Failed to complete campaign:`, error);
      throw error;
    }
  }

  /**
   * Check if all users in a camp have completed their campaigns
   * and update camp status to COMPLETED if so
   */
  private async checkAndUpdateCampCompletionStatus(
    campId: string,
  ): Promise<void> {
    try {
      // Get total users and completed users count
      const stats = await this.campaignStageProgressModel.aggregate([
        { $match: { campId } },
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            completedUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.COMPLETED] },
                  1,
                  0,
                ],
              },
            },
            activeUsers: {
              $sum: {
                $cond: [
                  { $eq: ['$overallStatus', USER_CAMPAIGN_STATUS.ACTIVE] },
                  1,
                  0,
                ],
              },
            },
          },
        },
      ]);

      const {
        totalUsers = 0,
        completedUsers = 0,
        activeUsers = 0,
      } = stats[0] || {};

      // If all users have completed and no users are still active, mark camp as completed
      if (
        totalUsers > 0 &&
        completedUsers === totalUsers &&
        activeUsers === 0
      ) {
        await this.campModel.findByIdAndUpdate(campId, {
          status: CAMP_STATUS.COMPLETED,
          updatedAt: new Date(),
        });
      }
    } catch (error) {
      this.logger.error(
        `Failed to check camp completion status for ${campId}:`,
        error,
      );
      // Don't throw error to avoid breaking user completion
    }
  }

  /**
   * Send stage email
   */
  private async sendStageEmail(
    progress: CampaignStageProgressDocument,
    emailItem: any,
  ): Promise<string> {
    try {
      // Get camp user details
      const campUser = await this.campUserModel.findOne({
        campId: progress.campId,
        userId: progress.userId,
      });

      if (!campUser) {
        throw new Error(`Camp user not found: ${progress.userId}`);
      }

      // Get camp first, then email template for sender details
      const camp = await this.campModel.findById(progress.campId).lean();
      if (!camp || !camp.emailTemplate) {
        throw new Error(`Camp or email template not found: ${progress.campId}`);
      }

      const template = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();
      if (!template) {
        throw new Error(`Email template not found: ${camp.emailTemplate}`);
      }

      // Get template requirements to understand what placeholders are expected
      const templateRequirements = template.requirements || [];

      // Prepare email variables based on template requirements and user data
      const emailVariables = this.buildEmailVariables(
        campUser,
        template,
        templateRequirements,
      );

      // Find all placeholders in content and add missing ones to variables
      const allPlaceholders = this.findAllPlaceholders(emailItem.content);

      // Add missing placeholders with fallback values
      let addedPlaceholders = 0;
      allPlaceholders.forEach((placeholder) => {
        if (!emailVariables[placeholder]) {
          // Provide intelligent fallback for missing placeholders
          if (
            placeholder.toLowerCase().includes('link') ||
            placeholder.toLowerCase().includes('url')
          ) {
            emailVariables[placeholder] = '#'; // Safe fallback for links
          } else if (placeholder.toLowerCase().includes('name')) {
            emailVariables[placeholder] =
              emailVariables.NAME || emailVariables.name || 'User';
          } else if (
            placeholder.toLowerCase().includes('position') ||
            placeholder.toLowerCase().includes('job')
          ) {
            emailVariables[placeholder] =
              emailVariables.JOB || emailVariables.job || 'Position';
          } else {
            emailVariables[placeholder] = placeholder; // Use placeholder name as fallback
          }
          addedPlaceholders++;
        }
      });

      // Replace variables in subject and content
      const subject = this.replaceVariables(emailItem.subject, emailVariables);
      let content = this.replaceVariables(emailItem.content, emailVariables);

      // Process DOWNLOAD_URL if required
      this.logger.log(`🔄 Processing email content for DOWNLOAD_URL...`);
      const processedContent =
        await this.emailContentProcessorService.processEmailContent(
          content,
          template,
        );
      content = processedContent.content;

      if (processedContent.downloadUrlResult) {
        this.logger.log(
          `📊 Download URL result: ${JSON.stringify(processedContent.downloadUrlResult, null, 2)}`,
        );
        if (processedContent.downloadUrlResult.success) {
          this.logger.log(
            `✅ Download URL created successfully: ${processedContent.downloadUrlResult.tinyUrl}`,
          );
        } else {
          this.logger.error(
            `❌ Download URL creation failed: ${processedContent.downloadUrlResult.error}`,
          );
        }
      } else {
        this.logger.log(`ℹ️ No DOWNLOAD_URL processing required`);
      }

      // Validate template data before sending
      if (!template.sender || !template.senderName) {
        throw new Error(
          `Email template missing sender information: sender=${template.sender}, senderName=${template.senderName}`,
        );
      }

      // Prepare email data
      const emailData = {
        toEmail: campUser.email,
        subject,
        htmlContent: content,
        fromName: template.senderName,
        fromEmail: template.sender,
        campaign: `camp_${progress.campId}`,
        emailType: progress.currentStage,
        replacements: emailVariables, // Pass variables for EMAIL_SERVICE_BASE_URL
      };

      // Log email data for debugging

      // Send email
      const result = await this.emailService.sendEmail(
        emailData,
        progress.campId,
      );

      // Update email tracking after successful send
      await this.updateEmailTracking(
        campUser,
        progress.currentStage,
        EMAIL_STATUS.SENT,
        new Date(),
        result.data?.messageId,
      );

      return result.message || 'unknown';
    } catch (error) {
      this.logger.error(`Failed to send stage email:`, error);
      throw error;
    }
  }

  /**
   * Build email variables based on template requirements and user data
   */
  private buildEmailVariables(
    campUser: any,
    emailTemplate: any,
    templateRequirements: string[],
  ): Record<string, string> {
    const variables: Record<string, string> = {};

    // Standard mappings from user data
    const userFullName =
      `${campUser.firstName || ''} ${campUser.lastName || ''}`.trim();
    const userName = userFullName || campUser.email || 'User';

    // Base variables always available
    variables.email = campUser.email || '';
    variables.firstName = campUser.firstName || 'User';
    variables.lastName = campUser.lastName || '';
    variables.userName = userName;
    variables.companyName = emailTemplate.brand || 'Company';

    // Add all metadata as variables first (since we store all CSV columns in metadata)
    if (campUser.metadata) {
      Object.entries(campUser.metadata).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          variables[key] = String(value);
        }
      });
    }

    // Process template requirements dynamically
    templateRequirements.forEach((requirement) => {
      const reqKey = requirement.trim();

      // Try to find value in multiple ways
      let value = '';

      // 1. Direct match from metadata
      if (campUser.metadata?.[reqKey]) {
        value = String(campUser.metadata[reqKey]);
      }
      // 2. Case-insensitive match from metadata
      else if (campUser.metadata?.[reqKey.toLowerCase()]) {
        value = String(campUser.metadata[reqKey.toLowerCase()]);
      } else if (campUser.metadata?.[reqKey.toUpperCase()]) {
        value = String(campUser.metadata[reqKey.toUpperCase()]);
      }
      // 3. Special mappings for common fields
      else if (reqKey.toUpperCase() === 'EMAIL') {
        value = campUser.email || '';
      } else if (
        reqKey.toUpperCase() === 'FULLNAME' ||
        reqKey.toUpperCase() === 'NAME'
      ) {
        value =
          campUser.metadata?.FULLNAME ||
          campUser.metadata?.fullname ||
          userFullName ||
          'User';
      } else if (reqKey.toUpperCase() === 'FIRSTNAME') {
        value = campUser.firstName || 'User';
      } else if (reqKey.toUpperCase() === 'LASTNAME') {
        value = campUser.lastName || '';
      } else {
        // Fallback: provide meaningful default values
        if (reqKey.toLowerCase().includes('name')) {
          value = userFullName || campUser.email || 'User';
        } else if (
          reqKey.toLowerCase().includes('position') ||
          reqKey.toLowerCase().includes('job')
        ) {
          value =
            campUser.metadata?.job || campUser.metadata?.JOB || 'Position';
        } else if (reqKey.toLowerCase().includes('country')) {
          value =
            campUser.metadata?.country ||
            campUser.metadata?.COUNTRY ||
            'Country';
        } else if (reqKey.toLowerCase().includes('email')) {
          value = campUser.email || '';
        } else {
          // Generic fallback
          value = reqKey;
        }
      }

      // Set variable with exact requirement key (preserve case)
      variables[reqKey] = value;

      // Also set common variations for flexibility
      variables[reqKey.toLowerCase()] = value;
      variables[reqKey.toUpperCase()] = value;

      // Special case for Name/NAME variations
      if (
        reqKey.toLowerCase() === 'name' ||
        reqKey.toLowerCase() === 'fullname'
      ) {
        variables.Name = value;
        variables.name = value;
        variables.NAME = value;
        variables.FULLNAME = value;
        variables.fullname = value;
      }

      // Special case for Position/JOB variations
      if (
        reqKey.toLowerCase() === 'position' ||
        reqKey.toLowerCase() === 'job'
      ) {
        variables.Position = value;
        variables.position = value;
        variables.POSITION = value;
        variables.JOB = value;
        variables.job = value;
      }
    });

    return variables;
  }

  /**
   * Find all placeholders in text
   */
  private findAllPlaceholders(text: string): string[] {
    const placeholderRegex = /{{\s*([^}]+)\s*}/g; // CORRECT regex - no backslash
    const placeholders: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = placeholderRegex.exec(text)) !== null) {
      const placeholder = match[1].trim();
      if (!placeholders.includes(placeholder)) {
        placeholders.push(placeholder);
      }
    }

    return placeholders;
  }

  /**
   * Update email tracking for a camp user
   */
  private async updateEmailTracking(
    campUser: any,
    emailType: string,
    status: EMAIL_STATUS,
    timestamp: Date,
    messageId?: string,
  ): Promise<void> {
    try {
      // Find existing email tracking for this email type
      let emailTrack = campUser.emailTracking.find(
        (track: any) => track.emailType === emailType,
      );

      if (!emailTrack) {
        // Create new email tracking entry
        emailTrack = {
          emailType,
          status,
          openCount: 0,
          clickCount: 0,
          messageId,
        };
        campUser.emailTracking.push(emailTrack);
      } else {
        // Update existing tracking
        emailTrack.status = status;
        if (messageId) {
          emailTrack.messageId = messageId;
        }
      }

      // Update timestamps based on status
      switch (status) {
        case EMAIL_STATUS.SENT:
          emailTrack.sentAt = timestamp;
          campUser.lastEmailSentAt = timestamp;
          break;
        case EMAIL_STATUS.DELIVERED:
          emailTrack.deliveredAt = timestamp;
          break;
        case EMAIL_STATUS.OPENED:
          emailTrack.openedAt = timestamp;
          emailTrack.openCount = (emailTrack.openCount || 0) + 1;
          break;
        case EMAIL_STATUS.CLICKED:
          emailTrack.clickedAt = timestamp;
          emailTrack.clickCount = (emailTrack.clickCount || 0) + 1;
          break;
        case EMAIL_STATUS.BOUNCED:
          emailTrack.bouncedAt = timestamp;
          break;
        case EMAIL_STATUS.FAILED:
          emailTrack.failedAt = timestamp;
          break;
      }

      // Save the updated camp user
      campUser.updatedAt = new Date();
      await campUser.save();
    } catch (error) {
      this.logger.error('Failed to update email tracking:', error);
    }
  }

  /**
   * Replace variables in text
   */
  private replaceVariables(
    text: string,
    variables: Record<string, string>,
  ): string {
    let result = text;
    Object.entries(variables).forEach(([key, value]) => {
      // Support both {{key}} and {{ key }} formats - escape special regex chars in key
      const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`{{\\s*${escapedKey}\\s*}}`, 'g');
      result = result.replace(regex, value || '');
    });
    return result;
  }
}
