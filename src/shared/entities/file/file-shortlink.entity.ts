import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';

export type FileShortLinkDocument = FileShortLink & Document;

@Schema({
  collection: 'file_short_links',
})
export class FileShortLink {
  @Prop({ required: true })
  file: string;

  @Prop({ required: false })
  logo: string;

  @Prop({ required: true })
  prefixPass: string;

  @Prop({ required: true })
  brand: string;

  @Prop({ required: false })
  expirationLink?: string;

  @Prop({ required: false, default: false })
  quantity: number;

  @Prop({ required: false, default: false })
  job: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const FileShortLinkSchema = SchemaFactory.createForClass(FileShortLink);

// Add a pre-query middleware to filter out soft-deleted documents
FileShortLinkSchema.pre<Query<FileShortLinkDocument, FileShortLinkDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
FileShortLinkSchema.pre<Query<FileShortLinkDocument, FileShortLinkDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

FileShortLinkSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
