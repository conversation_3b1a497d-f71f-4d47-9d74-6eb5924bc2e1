import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { EMAIL_STATUS } from 'src/shared/constants/camp.constant';

export type EmailTrackingDocument = EmailTracking & Document;

@Schema({ _id: false })
export class EmailTracking {
  @Prop({ required: true })
  emailType: string;

  @Prop({ required: true, enum: EMAIL_STATUS, default: EMAIL_STATUS.PENDING })
  status: EMAIL_STATUS;

  @Prop({ type: Date })
  sentAt?: Date;

  @Prop({ type: Date })
  deliveredAt?: Date;

  @Prop({ type: Date })
  openedAt?: Date;

  @Prop({ type: Date })
  clickedAt?: Date;

  @Prop({ type: Date })
  bouncedAt?: Date;

  @Prop({ type: Date })
  failedAt?: Date;

  @Prop({ type: String })
  failureReason?: string;

  @Prop({ type: Number, default: 0 })
  openCount: number;

  @Prop({ type: Number, default: 0 })
  clickCount: number;

  @Prop({ type: String })
  messageId?: string; // AWS SES message ID for tracking
}

export const EmailTrackingSchema = SchemaFactory.createForClass(EmailTracking);
