import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  EMAIL_CAMPAIGN_STAGE,
  STAGE_STATUS,
  USER_CAMPAIGN_STATUS,
} from '../../constants/camp.constant';

export type CampaignStageProgressDocument = CampaignStageProgress & Document;

@Schema({
  collection: 'campaign_stage_progress',
  timestamps: true,
})
export class CampaignStageProgress {
  @Prop({ required: true })
  campId: string;

  @Prop({ required: true })
  userId: string; // CampUser ID

  @Prop({ required: true })
  email: string;

  @Prop({
    required: true,
    enum: USER_CAMPAIGN_STATUS,
    default: USER_CAMPAIGN_STATUS.ACTIVE,
  })
  overallStatus: USER_CAMPAIGN_STATUS;

  @Prop({
    required: true,
    enum: EMAIL_CAMPAIGN_STAGE,
    default: EMAIL_CAMPAIGN_STAGE.INITIATION,
  })
  currentStage: EMAIL_CAMPAIGN_STAGE;

  @Prop({ required: true, default: 0 })
  currentEmailIndex: number; // Index của email hiện tại trong stage

  // Stage statuses
  @Prop({ enum: STAGE_STATUS, default: STAGE_STATUS.PENDING })
  initiationStatus: STAGE_STATUS;

  @Prop({ enum: STAGE_STATUS, default: STAGE_STATUS.PENDING })
  engagementStatus: STAGE_STATUS;

  @Prop({ enum: STAGE_STATUS, default: STAGE_STATUS.PENDING })
  conversionStatus: STAGE_STATUS;

  @Prop({ enum: STAGE_STATUS, default: STAGE_STATUS.PENDING })
  retentionStatus: STAGE_STATUS;

  // Stage completion timestamps
  @Prop({ type: Date })
  initiationCompletedAt?: Date;

  @Prop({ type: Date })
  engagementCompletedAt?: Date;

  @Prop({ type: Date })
  conversionCompletedAt?: Date;

  @Prop({ type: Date })
  retentionCompletedAt?: Date;

  // Stage start timestamps
  @Prop({ type: Date })
  initiationStartedAt?: Date;

  @Prop({ type: Date })
  engagementStartedAt?: Date;

  @Prop({ type: Date })
  conversionStartedAt?: Date;

  @Prop({ type: Date })
  retentionStartedAt?: Date;

  // Email tracking within current stage
  @Prop({ type: [String], default: [] })
  sentEmailIds: string[]; // Message IDs của emails đã gửi trong stage hiện tại

  @Prop({ type: [String], default: [] })
  openedEmailIds: string[]; // Message IDs của emails đã mở trong stage hiện tại

  @Prop({ type: [String], default: [] })
  clickedEmailIds: string[]; // Message IDs của emails đã click trong stage hiện tại

  // Next execution time for current stage
  @Prop({ type: Date })
  nextExecutionTime?: Date;

  // Track when last email was sent (for Conversion stage delay logic)
  @Prop({ type: Date })
  lastEmailSentAt?: Date;

  // Engagement stage specific - track if any email was opened
  @Prop({ type: Boolean, default: false })
  hasEngagementOpen: boolean;

  // Conversion stage specific - track last opened email
  @Prop({ type: String })
  lastOpenedEmailInConversion?: string;

  // Retention stage specific
  @Prop({ type: Number, default: 0 })
  retentionEmailsSent: number;

  @Prop({ type: Date })
  lastRetentionEmailSent?: Date;

  // Error tracking
  @Prop({ type: String })
  lastError?: string;

  @Prop({ type: Date })
  lastErrorAt?: Date;

  // Metadata for debugging
  @Prop({ type: Object })
  metadata?: Record<string, any>;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  updatedAt: Date;
}

export const CampaignStageProgressSchema = SchemaFactory.createForClass(
  CampaignStageProgress,
);

// Add indexes for better performance
CampaignStageProgressSchema.index({ campId: 1, userId: 1 }, { unique: true });
CampaignStageProgressSchema.index({ campId: 1, email: 1 });
CampaignStageProgressSchema.index({ campId: 1, currentStage: 1 });
CampaignStageProgressSchema.index({ campId: 1, overallStatus: 1 });
CampaignStageProgressSchema.index({ nextExecutionTime: 1 });
CampaignStageProgressSchema.index({ createdAt: -1 });
CampaignStageProgressSchema.index({ updatedAt: -1 });
