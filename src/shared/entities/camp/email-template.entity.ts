import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';

export type EmailTemplateDocument = EmailTemplate & Document;

// Enums for flow types
export enum FLOW_TYPE {
  START = 'start',
  REMIND = 'remind',
  END = 'end',
  PERIODIC = 'periodic',
}

export enum FLOW_ITEM_TYPE {
  EMAIL = 'email',
  DELAY = 'delay',
}

export enum PERIODIC_FREQUENCY {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

// Sub-schemas for flow items
@Schema({ _id: false })
export class EmailFlowItem {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true, enum: FLOW_TYPE })
  flowType: FLOW_TYPE;

  @Prop({ required: true, enum: FLOW_ITEM_TYPE, default: FLOW_ITEM_TYPE.EMAIL })
  type: FLOW_ITEM_TYPE;

  @Prop({ type: Number, required: false, default: 0 })
  delay?: number; // Delay in seconds before sending this email

  @Prop({ type: [String], required: false, default: [] })
  requirements?: string[];
}

@Schema({ _id: false })
export class DelayFlowItem {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true })
  delay: number; // delay in seconds

  @Prop({ required: true, enum: FLOW_ITEM_TYPE, default: FLOW_ITEM_TYPE.DELAY })
  type: FLOW_ITEM_TYPE;
}

// Union type for flow items
export type FlowItem = EmailFlowItem | DelayFlowItem;

@Schema({ _id: false })
export class PeriodicConfig {
  @Prop({ required: false })
  startDate?: Date;

  @Prop({ required: false })
  endDate?: Date;

  @Prop({ required: false, enum: PERIODIC_FREQUENCY })
  frequency?: PERIODIC_FREQUENCY;

  @Prop({ required: false })
  maxSendCount?: number;

  @Prop({ required: false, default: false })
  enabled?: boolean;
}

@Schema({
  collection: 'email_templates',
  timestamps: true,
})
export class EmailTemplate {
  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ required: true })
  sender: string;

  @Prop({ required: true })
  senderName: string;

  @Prop({ required: true })
  brand: string;

  @Prop({ required: true })
  domain: string;

  @Prop({ required: true })
  prefix: string;

  @Prop({ required: false, default: null })
  file?: string;

  @Prop({
    type: [String],
    required: false,
    default: ['EMAIL', 'COUNTRY', 'FULLNAME', 'JOB'],
    description: 'Required columns in user CSV file for validation',
  })
  requirements?: string[];

  @Prop({ required: false })
  expirationLink?: string;

  @Prop({
    type: [{ type: Object }],
    default: [],
    validate: {
      validator: function (flows: any[]) {
        return flows.every(
          (flow) =>
            (flow.type === FLOW_ITEM_TYPE.EMAIL &&
              flow.subject &&
              flow.content &&
              flow.flowType) ||
            (flow.type === FLOW_ITEM_TYPE.DELAY &&
              typeof flow.delay === 'number'),
        );
      },
      message: 'Invalid flow item structure',
    },
  })
  startFlow: FlowItem[];

  @Prop({
    type: [{ type: Object }],
    default: [],
    validate: {
      validator: function (flows: any[]) {
        return flows.every(
          (flow) =>
            (flow.type === FLOW_ITEM_TYPE.EMAIL &&
              flow.subject &&
              flow.content &&
              flow.flowType) ||
            (flow.type === FLOW_ITEM_TYPE.DELAY &&
              typeof flow.delay === 'number'),
        );
      },
      message: 'Invalid flow item structure',
    },
  })
  remindFlow: FlowItem[];

  @Prop({
    type: [{ type: Object }],
    default: [],
    validate: {
      validator: function (flows: any[]) {
        return flows.every(
          (flow) =>
            (flow.type === FLOW_ITEM_TYPE.EMAIL &&
              flow.subject &&
              flow.content &&
              flow.flowType) ||
            (flow.type === FLOW_ITEM_TYPE.DELAY &&
              typeof flow.delay === 'number'),
        );
      },
      message: 'Invalid flow item structure',
    },
  })
  endFlow: FlowItem[];

  @Prop({
    type: [{ type: Object }],
    default: [],
    validate: {
      validator: function (flows: any[]) {
        return flows.every(
          (flow) =>
            (flow.type === FLOW_ITEM_TYPE.EMAIL &&
              flow.subject &&
              flow.content &&
              flow.flowType) ||
            (flow.type === FLOW_ITEM_TYPE.DELAY &&
              typeof flow.delay === 'number'),
        );
      },
      message: 'Invalid flow item structure',
    },
  })
  periodicFlow: FlowItem[];

  @Prop({ type: PeriodicConfig, required: false })
  periodicConfig?: PeriodicConfig;

  @Prop({ required: false })
  startDate?: Date;

  @Prop({ required: false })
  endDate?: Date;

  @Prop({ required: false, default: 0 })
  totalSubscribers: number;

  @Prop({ required: false, default: 0 })
  totalEmailsSent: number;

  @Prop({ required: false, default: 0 })
  totalOpened: number;

  @Prop({ required: false, default: 0 })
  totalClicked: number;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate);

addAuthStaticMethods(EmailTemplateSchema);

// Add indexes for better performance
EmailTemplateSchema.index({ brand: 1 });
EmailTemplateSchema.index({ createdAt: -1 });
EmailTemplateSchema.index({ startDate: 1, endDate: 1 });
EmailTemplateSchema.index({ deletedAt: 1 }, { sparse: true });

// Add a pre-query middleware to filter out soft-deleted documents
EmailTemplateSchema.pre<Query<EmailTemplateDocument, EmailTemplateDocument>>(
  /^find/,
  function (next: any) {
    this.where({ deletedAt: null });
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
EmailTemplateSchema.pre<Query<EmailTemplateDocument, EmailTemplateDocument>>(
  /^count/,
  function (next: any) {
    this.where({ deletedAt: null });
    next();
  },
);

EmailTemplateSchema.pre('aggregate', function (next: any) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage: any) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
