import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';
import { CAMP_STATUS } from '../../constants/camp.constant';

export type CampDocument = Camp & Document;

@Schema({
  collection: 'camps',
})
export class Camp {
  @Prop({ required: true })
  brand: string;

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  file: string;

  @Prop({ required: false, ref: 'EmailTemplate' })
  emailTemplate: string;

  @Prop({ required: false })
  webhookUrl?: string;

  @Prop({ enum: CAMP_STATUS, default: CAMP_STATUS.DRAFT })
  status: CAMP_STATUS;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: String })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ type: Date })
  updatedAt: Date;

  @Prop({ type: String })
  updatedBy: number;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: String })
  deletedBy?: string;
}

export const CampSchema = SchemaFactory.createForClass(Camp);

addAuthStaticMethods(CampSchema);

// Add indexes for better performance
CampSchema.index({ brand: 1, status: 1 });
CampSchema.index({ status: 1 });
CampSchema.index({ createdAt: -1 });
CampSchema.index({ startDate: 1 });
CampSchema.index({ deletedAt: 1 }, { sparse: true });

// Add a pre-query middleware to filter out soft-deleted documents
CampSchema.pre<Query<CampDocument, CampDocument>>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
CampSchema.pre<Query<CampDocument, CampDocument>>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

CampSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
