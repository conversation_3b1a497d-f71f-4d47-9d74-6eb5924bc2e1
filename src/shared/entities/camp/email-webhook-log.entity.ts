import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailWebhookLogDocument = EmailWebhookLog & Document;

@Schema({
  collection: 'email_webhook_logs',
  timestamps: true,
})
export class EmailWebhookLog {
  @Prop({ required: true })
  campId: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  emailType: string;

  @Prop({ required: true })
  status: string;

  @Prop({ required: false })
  messageId?: string;

  @Prop({ required: false })
  subject?: string;

  @Prop({ required: false })
  timestamp?: Date;

  @Prop({ required: false })
  errorMessage?: string;

  @Prop({ required: false })
  bounceReason?: string;

  @Prop({ required: false })
  clickedUrl?: string;

  @Prop({ required: false })
  userAgent?: string;

  @Prop({ required: false })
  ipAddress?: string;

  @Prop({ type: Object, required: false })
  rawPayload?: any;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

export const EmailWebhookLogSchema =
  SchemaFactory.createForClass(EmailWebhookLog);

// Add indexes for better performance
EmailWebhookLogSchema.index({ campId: 1, email: 1 });
EmailWebhookLogSchema.index({ campId: 1, status: 1 });
EmailWebhookLogSchema.index({ createdAt: -1 });
EmailWebhookLogSchema.index({ messageId: 1 }, { sparse: true });
