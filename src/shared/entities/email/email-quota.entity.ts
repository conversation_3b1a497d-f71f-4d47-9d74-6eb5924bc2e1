import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailQuotaDocument = EmailQuota & Document;

@Schema({
  collection: 'email_quotas',
  timestamps: true,
})
export class EmailQuota {
  @Prop({ required: true, unique: true })
  date: string; // YYYY-MM-DD format

  @Prop({ required: true, default: 0 })
  emailsSent: number;

  @Prop({ required: true, default: 50000 })
  dailyLimit: number;

  @Prop({ required: true, default: false })
  isQuotaExceeded: boolean;

  @Prop({ required: true })
  lastResetAt: Date;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  updatedAt: Date;
}

export const EmailQuotaSchema = SchemaFactory.createForClass(EmailQuota);

// Add indexes for better performance
// Note: date field already has unique index from @Prop({ unique: true })
EmailQuotaSchema.index({ createdAt: -1 });
