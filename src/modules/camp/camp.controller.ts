import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Body,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  Patch,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { CampService } from './camp.service';
import { EmailQuotaService } from 'src/shared/services/email-quota.service';
import { CreateCampDTO, GetCampQueryDTO, UpdateCampDTO } from './dto/camp.dto';
import {
  GetCampResponseDTO,
  GetCampsResponseDTO,
  CampaignOverviewResponseDTO,
  EmailPerformanceResponseDTO,
  UserSegmentAnalysisResponseDTO,
  TimeAnalysisResponseDTO,
  ErrorAnalysisResponseDTO,
} from './dto/camp.dto';
import {
  UpdateEmailTrackingDTO,
  CampUserResponseDTO,
} from './dto/camp-user.dto';
import {
  MonitoringEmailsQueryDTO,
  MonitoringOverviewResponseDTO,
  MonitoringEmailsResponseDTO,
  MonitoringErrorResponseDTO,
} from './dto/monitoring.dto';
import { Response } from 'express';

@ApiTags('Camps')
@Controller('camps')
export class CampController {
  constructor(
    private readonly campService: CampService,
    private readonly emailQuotaService: EmailQuotaService,
  ) {}

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get()
  @ApiOperation({
    summary: 'Get campaigns',
    description:
      'Retrieve a paginated list of campaigns with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaigns retrieved successfully',
    type: GetCampsResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getCamps(
    @Query() getCampsQueryDTO: GetCampQueryDTO,
  ): Promise<GetCampsResponseDTO> {
    return this.campService.getCamps(getCampsQueryDTO);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id')
  @ApiOperation({
    summary: 'Get campaign detail',
    description: 'Retrieve detailed information about a specific campaign',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign retrieved successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getCampDetail(@Param('id') id: string): Promise<GetCampResponseDTO> {
    return this.campService.getCampDetail(id);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post()
  @ApiOperation({
    summary: 'Create campaign',
    description: 'Create a new campaign with CSV file upload for user data',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Campaign data with CSV file',
    schema: {
      type: 'object',
      properties: {
        brand: {
          type: 'string',
          description: 'Brand name',
          example: 'example.com',
        },
        startDate: {
          type: 'string',
          description: 'Campaign start date',
          example: '2024-01-01T00:00:00.000Z',
        },
        emailTemplate: {
          type: 'string',
          description: 'Email template ID',
          example: '507f1f77bcf86cd799439012',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'CSV file with user data (EMAIL,COUNTRY,FULLNAME,JOB)',
        },
      },
      required: ['brand', 'startDate', 'emailTemplate', 'file'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Campaign created successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or file format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.csv$/)) {
          return callback(
            new BadRequestException('Only .csv files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async createCamp(
    @Body() createCampDTO: CreateCampDTO,
    @UploadedFile() file: Express.Multer.File,
    @RequestUser() user: User,
  ): Promise<GetCampResponseDTO> {
    return this.campService.createCamp(createCampDTO, file, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Patch(':id')
  @ApiOperation({
    summary: 'Update campaign',
    description: 'Update an existing campaign with optional CSV file upload',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Campaign update data with optional CSV file',
    schema: {
      type: 'object',
      properties: {
        brand: {
          type: 'string',
          description: 'Brand name',
          example: 'example.com',
        },
        startDate: {
          type: 'string',
          description: 'Campaign start date',
          example: '2024-01-01T00:00:00.000Z',
        },
        emailTemplate: {
          type: 'string',
          description: 'Email template ID',
          example: '507f1f77bcf86cd799439012',
        },
        file: {
          type: 'string',
          format: 'binary',
          description:
            'CSV file with user data (EMAIL,COUNTRY,FULLNAME,JOB) - optional',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign updated successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.csv$/)) {
          return callback(
            new BadRequestException('Only .csv files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async updateCamp(
    @Param('id') id: string,
    @Body() updateCampDTO: UpdateCampDTO,
    @RequestUser() user: User,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<GetCampResponseDTO> {
    return this.campService.updateCamp(id, updateCampDTO, user, file);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Delete(':id')
  @ApiOperation({
    summary: 'Delete campaign',
    description: 'Soft delete a campaign (sets deletedAt timestamp)',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteCamp(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.campService.deleteCamp(id, user);

    return {
      success: true,
    };
  }

  @Post('webhook')
  @ApiOperation({
    summary: 'Handle AWS SES webhook',
    description:
      'Global endpoint to receive webhook notifications from AWS SES for all campaigns. Camp ID will be extracted from email tags or headers.',
  })
  @ApiBody({
    description: 'AWS SES webhook payload (SNS notification format)',
    schema: {
      type: 'object',
      properties: {
        Type: { type: 'string', example: 'Notification' },
        MessageId: { type: 'string', example: 'sns-message-id-123' },
        TopicArn: {
          type: 'string',
          example: 'arn:aws:sns:us-east-1:123456789012:ses-events',
        },
        Subject: { type: 'string', example: 'Amazon SES Email Event' },
        Message: {
          type: 'string',
          example:
            '{"eventType":"delivery","mail":{"timestamp":"2024-12-19T10:30:00.000Z","source":"<EMAIL>","messageId":"0000014a-f4d4-4f62-b326-example","destination":["<EMAIL>"],"commonHeaders":{"from":["<EMAIL>"],"to":["<EMAIL>"],"messageId":"<<EMAIL>>","subject":"Test Subject"}},"delivery":{"timestamp":"2024-12-19T10:30:01.000Z","processingTimeMillis":1000,"recipients":["<EMAIL>"],"smtpResponse":"250 2.0.0 OK"}}',
          description: 'JSON string containing the actual SES event data',
        },
        Timestamp: { type: 'string', example: '2024-12-19T10:30:00.000Z' },
        SignatureVersion: { type: 'string', example: '1' },
        Signature: { type: 'string', example: 'signature-hash' },
        SigningCertURL: {
          type: 'string',
          example: 'https://sns.us-east-1.amazonaws.com/cert.pem',
        },
        UnsubscribeURL: {
          type: 'string',
          example: 'https://sns.us-east-1.amazonaws.com/unsubscribe',
        },
      },
      required: ['Type', 'Message'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'AWS SES webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Webhook processed successfully for event type: delivery',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid AWS SES webhook payload or format',
  })
  @ApiResponse({
    status: 404,
    description: 'Camp not found',
  })
  async handleEmailWebhook(
    @Body() webhookPayload: any,
  ): Promise<{ success: boolean; message: string }> {
    return this.campService.handleEmailWebhook(webhookPayload);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/download')
  @ApiOperation({
    summary: 'Download campaign file',
    description: 'Download the CSV file associated with the campaign',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or file not available',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } = await this.campService.downloadFile(id);

    res.set({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="file"`,
    });

    stream.pipe(res);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':campaignId/overview')
  @ApiOperation({
    summary: 'Get campaign overview',
    description:
      'Returns overview information about a campaign based on camp_users table and mail template duration',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign overview retrieved successfully',
    type: CampaignOverviewResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getCampaignOverview(
    @Param('campaignId') campaignId: string,
  ): Promise<CampaignOverviewResponseDTO> {
    return this.campService.getCampaignOverview(campaignId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':campaignId/email-performance')
  @ApiOperation({
    summary: 'Get email performance statistics',
    description:
      'Returns performance metrics for each email type in the campaign',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Email performance statistics retrieved successfully',
    type: EmailPerformanceResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getEmailPerformance(
    @Param('campaignId') campaignId: string,
  ): Promise<EmailPerformanceResponseDTO> {
    return this.campService.getEmailPerformance(campaignId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':campaignId/user-segments')
  @ApiOperation({
    summary: 'Get user segment analysis',
    description:
      'Returns campaign performance metrics grouped by user segments (position/job)',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'User segment analysis retrieved successfully',
    type: UserSegmentAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getUserSegmentAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<UserSegmentAnalysisResponseDTO> {
    return this.campService.getUserSegmentAnalysis(campaignId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':campaignId/time-analysis')
  @ApiOperation({
    summary: 'Get time analysis statistics',
    description:
      'Returns time-based analytics including average time to open emails and conversion trends',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Time analysis statistics retrieved successfully',
    type: TimeAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getTimeAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<TimeAnalysisResponseDTO> {
    return this.campService.getTimeAnalysis(campaignId);
  }

  @Patch(':campaignId/users/:email/email-tracking')
  @ApiOperation({
    summary: 'Update email tracking',
    description: 'Updates email tracking status for a user in the campaign',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiParam({
    name: 'email',
    description: 'User email address',
    example: '<EMAIL>',
  })
  @ApiBody({
    description: 'Email tracking update data',
    type: UpdateEmailTrackingDTO,
  })
  @ApiResponse({
    status: 200,
    description: 'Email tracking updated successfully',
    type: CampUserResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'User not found in campaign',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async updateEmailTracking(
    @Param('campaignId') campaignId: string,
    @Param('email') email: string,
    @Body() updateDto: UpdateEmailTrackingDTO,
  ): Promise<CampUserResponseDTO> {
    return this.campService.updateEmailTracking(email, campaignId, updateDto);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':campaignId/errors')
  @ApiOperation({
    summary: 'Get error analysis statistics',
    description:
      'Returns error analysis including failed emails, bounce rate, and non-interactive users',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Error analysis statistics retrieved successfully',
    type: ErrorAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getErrorAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<ErrorAnalysisResponseDTO> {
    return this.campService.getErrorAnalysis(campaignId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':id/start-campaign')
  @ApiOperation({
    summary: 'Start email campaign for camp',
    description:
      'Start executing email campaign flows for all users in the camp',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign started successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        executionsStarted: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Camp not found or no email campaign associated',
  })
  async startCampaign(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsStarted: number;
  }> {
    return this.campService.startCampaign(id, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':id/stop-campaign')
  @ApiOperation({
    summary: 'Stop email campaign for camp',
    description: 'Stop executing email campaign flows for the camp',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign stopped successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        executionsStopped: { type: 'number' },
      },
    },
  })
  async stopCampaign(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsStopped: number;
  }> {
    return this.campService.stopCampaign(id, user);
  }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Get(':id/campaign-status')
  // @ApiOperation({
  //   summary: 'Get campaign execution status',
  //   description:
  //     'Get the current status of email campaign execution for the camp',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Campaign execution status retrieved successfully',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       totalExecutions: {
  //         type: 'number',
  //         description: 'Total number of executions',
  //       },
  //       pending: {
  //         type: 'number',
  //         description: 'Number of pending executions',
  //       },
  //       running: {
  //         type: 'number',
  //         description: 'Number of running executions',
  //       },
  //       completed: {
  //         type: 'number',
  //         description: 'Number of completed executions',
  //       },
  //       failed: { type: 'number', description: 'Number of failed executions' },
  //       paused: { type: 'number', description: 'Number of paused executions' },
  //       cancelled: {
  //         type: 'number',
  //         description: 'Number of cancelled executions',
  //       },
  //     },
  //   },
  // })
  // async getCampaignExecutionStatus(
  //   @Param('id') id: string,
  //   @RequestUser() user: User,
  // ): Promise<{
  //   totalExecutions: number;
  //   pending: number;
  //   running: number;
  //   completed: number;
  //   failed: number;
  //   paused: number;
  //   cancelled: number;
  // }> {
  //   return this.campService.getCampaignExecutionStatus(id, user);
  // }

  @Post(':id/pause-campaign')
  @ApiOperation({
    summary: 'Pause email campaign for camp',
    description:
      'Temporarily pause executing email campaign flows for the camp. Can be resumed later.',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign paused successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        executionsPaused: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Camp not found or no email campaign associated',
  })
  async pauseCampaign(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsPaused: number;
  }> {
    return this.campService.pauseCampaign(id, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':id/resume-campaign')
  @ApiOperation({
    summary: 'Resume email campaign for camp',
    description: 'Resume previously paused email campaign flows for the camp.',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign resumed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        executionsResumed: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Camp not found or no paused executions',
  })
  async resumeCampaign(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsResumed: number;
  }> {
    return this.campService.resumeCampaign(id, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':id/cancel-campaign')
  @ApiOperation({
    summary: 'Cancel email campaign for camp',
    description:
      'Permanently cancel email campaign flows for the camp. Cannot be resumed.',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        executionsCancelled: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Camp not found or no email campaign associated',
  })
  async cancelCampaign(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsCancelled: number;
  }> {
    return this.campService.cancelCampaign(id, user);
  }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Get('email-quota/status')
  // @ApiOperation({
  //   summary: 'Get email quota status',
  //   description:
  //     'Get current daily email quota status including usage and limits from our internal tracking system',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Email quota status retrieved successfully',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       date: { type: 'string', example: '2024-12-19' },
  //       emailsSent: { type: 'number', example: 1250 },
  //       dailyLimit: { type: 'number', example: 50000 },
  //       remaining: { type: 'number', example: 48750 },
  //       isQuotaExceeded: { type: 'boolean', example: false },
  //       resetTime: { type: 'string', example: '2024-12-20T00:00:00.000Z' },
  //     },
  //   },
  // })
  // async getEmailQuotaStatus(): Promise<{
  //   date: string;
  //   emailsSent: number;
  //   dailyLimit: number;
  //   remaining: number;
  //   isQuotaExceeded: boolean;
  //   resetTime: Date;
  // }> {
  //   return this.emailQuotaService.getQuotaStatus();
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Get(':id/campaign-progress')
  // @ApiOperation({
  //   summary: 'Get campaign stage progress',
  //   description:
  //     'Get detailed progress of 4-stage email campaign for all users in the camp',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Campaign progress retrieved successfully',
  // })
  // async getCampaignProgress(
  //   @Param('id') id: string,
  //   @RequestUser() user: User,
  // ): Promise<any> {
  //   return this.campService.getCampaignProgress(id, user);
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Get(':id/campaign-stats')
  // @ApiOperation({
  //   summary: 'Get campaign statistics',
  //   description:
  //     'Get statistics for 4-stage email campaign including stage distribution and user status',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Campaign statistics retrieved successfully',
  // })
  // async getCampaignStats(
  //   @Param('id') id: string,
  //   @RequestUser() user: User,
  // ): Promise<any> {
  //   return this.campService.getCampaignStats(id, user);
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Post(':id/pause-user-campaign')
  // @ApiOperation({
  //   summary: 'Pause campaign for specific user',
  //   description: 'Pause 4-stage email campaign for a specific user in the camp',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User campaign paused successfully',
  // })
  // async pauseUserCampaign(
  //   @Param('id') id: string,
  //   @Body() body: { email: string },
  //   @RequestUser() user: User,
  // ): Promise<{ success: boolean; message: string }> {
  //   return this.campService.pauseUserCampaign(id, body.email, user);
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Post(':id/resume-user-campaign')
  // @ApiOperation({
  //   summary: 'Resume campaign for specific user',
  //   description:
  //     'Resume 4-stage email campaign for a specific user in the camp',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'User campaign resumed successfully',
  // })
  // async resumeUserCampaign(
  //   @Param('id') id: string,
  //   @Body() body: { email: string },
  //   @RequestUser() user: User,
  // ): Promise<{ success: boolean; message: string }> {
  //   return this.campService.resumeUserCampaign(id, body.email, user);
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Post('clear-all-processes')
  // @ApiOperation({
  //   summary: 'Clear all running campaign processes',
  //   description:
  //     'Stop and clear all running campaign executions and stage progresses',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'All processes cleared successfully',
  // })
  // async clearAllProcesses(@RequestUser() user: User): Promise<{
  //   success: boolean;
  //   message: string;
  //   clearedExecutions: number;
  //   clearedProgresses: number;
  // }> {
  //   return this.campService.clearAllProcesses(user);
  // }

  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(AuthGuard)
  // @Post(':id/clear-camp-processes')
  // @ApiOperation({
  //   summary: 'Clear all processes for specific camp',
  //   description: 'Stop and clear all running processes for a specific camp',
  // })
  // @ApiParam({ name: 'id', description: 'Camp ID' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Camp processes cleared successfully',
  // })
  // async clearCampProcesses(
  //   @Param('id') id: string,
  //   @RequestUser() user: User,
  // ): Promise<{
  //   success: boolean;
  //   message: string;
  //   clearedExecutions: number;
  //   clearedProgresses: number;
  // }> {
  //   return this.campService.clearCampProcesses(id, user);
  // }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/webhook-logs')
  @ApiOperation({
    summary: 'Get webhook logs for camp',
    description:
      'Get webhook logs for a specific camp with optional status filtering',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: [
      'send',
      'delivery',
      'bounce',
      'complaint',
      'reject',
      'open',
      'click',
      'renderingFailure',
      'deliveryDelay',
    ],
    description:
      'Filter by AWS SES webhook event type: send (email sent to AWS SES), delivery (successfully delivered to recipient), bounce (delivery failed - permanent or temporary), complaint (recipient marked as spam), reject (rejected by AWS SES before sending), open (email opened by recipient), click (link clicked in email), renderingFailure (template rendering failed), deliveryDelay (temporary delivery delay)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of logs to return (default: 50)',
  })
  @ApiQuery({
    name: 'skip',
    required: false,
    type: Number,
    description: 'Number of logs to skip (default: 0)',
  })
  @ApiQuery({
    name: 'email',
    required: false,
    type: String,
    description: 'Filter by specific email address',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Filter logs from this date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'Filter logs until this date (ISO string)',
  })
  @ApiQuery({
    name: 'processingStatus',
    required: false,
    enum: ['pending', 'processed', 'failed', 'error', 'warning', 'success'],
    description:
      'Filter by webhook processing status: pending (not yet processed), processed (successfully processed), failed (processing failed), error (processing error), warning (processed with warnings), success (processed successfully)',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook logs retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string' },
              campId: { type: 'string' },
              email: { type: 'string' },
              messageId: { type: 'string' },
              status: { type: 'string' },
              eventType: { type: 'string' },
              timestamp: { type: 'string' },
              webhookData: {
                type: 'object',
                description: 'Detailed webhook data from AWS SES',
                properties: {
                  subject: {
                    type: 'string',
                    description: 'Email subject line',
                    example: 'Campaign Email Subject',
                  },
                  fromEmail: {
                    type: 'string',
                    description: 'Sender email address',
                    example: '<EMAIL>',
                  },
                  bounceType: {
                    type: 'string',
                    enum: ['Permanent', 'Transient', 'Undetermined'],
                    description: 'Type of bounce (for bounce events)',
                    example: 'Permanent',
                  },
                  bounceSubType: {
                    type: 'string',
                    enum: [
                      'General',
                      'NoEmail',
                      'Suppressed',
                      'MailboxFull',
                      'MessageTooLarge',
                      'ContentRejected',
                      'AttachmentRejected',
                    ],
                    description: 'Specific bounce reason (for bounce events)',
                    example: 'NoEmail',
                  },
                  complaintType: {
                    type: 'string',
                    enum: [
                      'abuse',
                      'auth-failure',
                      'fraud',
                      'not-spam',
                      'other',
                      'virus',
                    ],
                    description: 'Type of complaint (for complaint events)',
                    example: 'abuse',
                  },
                  clickedUrl: {
                    type: 'string',
                    description: 'URL that was clicked (for click events)',
                    example: 'https://example.com/campaign-link',
                  },
                  ipAddress: {
                    type: 'string',
                    description:
                      'IP address of recipient (for open/click events)',
                    example: '***********',
                  },
                  deliveryTimestamp: {
                    type: 'string',
                    format: 'date-time',
                    description:
                      'When email was delivered (for delivery events)',
                    example: '2024-12-19T10:30:01.000Z',
                  },
                  bounceTimestamp: {
                    type: 'string',
                    format: 'date-time',
                    description: 'When bounce occurred (for bounce events)',
                    example: '2024-12-19T10:30:05.000Z',
                  },
                  complaintTimestamp: {
                    type: 'string',
                    format: 'date-time',
                    description:
                      'When complaint was made (for complaint events)',
                    example: '2024-12-19T10:35:00.000Z',
                  },
                  processingStatus: {
                    type: 'string',
                    enum: [
                      'pending',
                      'processed',
                      'failed',
                      'error',
                      'warning',
                      'success',
                    ],
                    description: 'Status of webhook processing',
                    example: 'processed',
                  },
                  rawPayload: {
                    type: 'object',
                    description:
                      'Complete raw webhook payload from AWS SES for debugging',
                    example: {
                      version: '0',
                      id: '10a75455-c664-decb-c3b1-86b75bc683e5',
                      'detail-type': 'SES Email Event',
                      source: 'aws.ses',
                      account: '************',
                      time: '2024-12-19T10:30:01Z',
                      region: 'us-east-1',
                      detail: {
                        eventType: 'delivery',
                        mail: {
                          timestamp: '2024-12-19T10:30:00.000Z',
                          source: '<EMAIL>',
                          messageId: '0000014a-f4d4-4f62-b326-example',
                          destination: ['<EMAIL>'],
                        },
                      },
                    },
                  },
                },
              },
              createdAt: { type: 'string' },
            },
          },
        },
        total: { type: 'number' },
        skip: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Camp not found',
  })
  async getCampWebhookLogs(
    @Param('id') id: string,
    @RequestUser() user: User,
    @Query('status') status?: string,
    @Query('limit') limit?: number,
    @Query('skip') skip?: number,
    @Query('email') email?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('processingStatus') processingStatus?: string,
  ): Promise<{
    success: boolean;
    data: any[];
    total: number;
    skip: number;
    limit: number;
  }> {
    return this.campService.getCampWebhookLogs(id, {
      status,
      limit: limit || 50,
      skip: skip || 0,
      email,
      startDate,
      endDate,
      processingStatus,
    });
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/email-monitoring/:emailId/details')
  @ApiOperation({
    summary: 'Get detailed email information',
    description:
      'Get detailed information for a specific email (for the eye icon click)',
  })
  @ApiParam({ name: 'id', description: 'Camp ID' })
  @ApiParam({ name: 'emailId', description: 'Email tracking ID' })
  @ApiResponse({
    status: 200,
    description: 'Email details retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            recipient: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                email: { type: 'string' },
              },
            },
            subject: { type: 'string' },
            content: { type: 'string' },
            status: { type: 'string' },
            timeline: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  event: { type: 'string' },
                  timestamp: { type: 'string' },
                  details: { type: 'object' },
                },
              },
            },
            device: { type: 'string' },
            location: { type: 'string' },
            ipAddress: { type: 'string' },
            userAgent: { type: 'string' },
            webhookLogs: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  eventType: { type: 'string' },
                  timestamp: { type: 'string' },
                  rawData: { type: 'object' },
                },
              },
            },
          },
        },
      },
    },
  })
  async getEmailDetails(
    @Param('id') id: string,
    @Param('emailId') emailId: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
    data: any;
  }> {
    return this.campService.getEmailDetails(id, emailId);
  }

  /**
   * NEW API 1: Campaign Overview & Statistics
   * Endpoint: GET /api/camps/{id}/monitoring-overview
   * Purpose: Campaign overview and statistics (cacheable, 30s refresh)
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/monitoring-overview')
  @ApiOperation({
    summary: 'Get monitoring overview',
    description:
      'Get campaign overview and statistics for monitoring dashboard. Optimized for caching with 30s refresh rate.',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Monitoring overview retrieved successfully',
    type: MonitoringOverviewResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
    type: MonitoringErrorResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getMonitoringOverview(
    @Param('id') id: string,
  ): Promise<MonitoringOverviewResponseDTO> {
    return this.campService.getMonitoringOverview(id);
  }

  /**
   * Export Campaign Monitoring Data
   * Endpoint: GET /api/camps/{id}/monitoring-export
   * Purpose: Export campaign monitoring data in various formats (CSV, XLSX, PDF, JSON)
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/monitoring-export')
  @ApiOperation({
    summary: 'Export monitoring data',
    description:
      'Export campaign monitoring data in specified format (csv, xlsx, pdf, json)',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiQuery({
    name: 'type',
    description: 'Export format type',
    enum: ['csv', 'xlsx', 'pdf', 'json'],
    example: 'csv',
  })
  @ApiResponse({
    status: 200,
    description: 'File exported successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
      'application/json': {
        schema: {
          type: 'object',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or invalid export type',
    type: MonitoringErrorResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async exportMonitoringData(
    @Param('id') id: string,
    @Query('type') type: 'csv' | 'xlsx' | 'pdf' | 'json',
    @Res() res: Response,
  ): Promise<void> {
    return this.campService.exportMonitoringData(id, type, res);
  }

  /**
   * NEW API 2: Email List Real-time
   * Endpoint: GET /api/camps/{id}/monitoring-emails
   * Purpose: Real-time email list (no cache, 5s refresh)
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get(':id/monitoring-emails')
  @ApiOperation({
    summary: 'Get monitoring emails',
    description:
      'Get real-time email list with filtering and pagination. Optimized for 5s refresh rate.',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiQuery({
    name: 'skip',
    description: 'Pagination offset',
    required: false,
    example: 0,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page (max 100)',
    required: false,
    example: 50,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status (comma separated)',
    required: false,
    example: 'pending,sent,delivered',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search in email/name/subject',
    required: false,
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort field',
    required: false,
    example: 'sentAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort direction',
    required: false,
    example: 'desc',
  })
  @ApiResponse({
    status: 200,
    description: 'Monitoring emails retrieved successfully',
    type: MonitoringEmailsResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or invalid parameters',
    type: MonitoringErrorResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getMonitoringEmails(
    @Param('id') id: string,
    @Query() query: MonitoringEmailsQueryDTO,
  ): Promise<MonitoringEmailsResponseDTO> {
    return this.campService.getMonitoringEmails(id, query);
  }
}
