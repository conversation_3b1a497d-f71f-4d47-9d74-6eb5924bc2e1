import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { CampService } from './camp.service';

@Controller('camp')
export class TestWebhookCampController {
  constructor(private readonly campService: CampService) {}

  @Post('webhook')
  @HttpCode(200)
  async testWebhook(@Body() body: any): Promise<any> {
    // Log the received webhook data
    console.log('Received webhook data:', body);

    // Process the webhook data using the CampService
    const result = await this.campService.handleEmailWebhook(body);

    // Return a response indicating success
    return { success: true };
  }
}
