import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import { Camp, CampSchema } from 'src/shared/entities/camp/camp.entity';
import {
  CampUser,
  CampUserSchema,
} from 'src/shared/entities/camp/camp-user.entity';

import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { CampaignExecutionService } from 'src/shared/services/campaign-execution.service';
import { EmailService } from 'src/shared/services/email-service.service';
import { EmailQuotaService } from 'src/shared/services/email-quota.service';

import { CampaignStageFlowService } from 'src/shared/services/campaign-stage-flow.service';
import { EmailContentProcessorService } from 'src/shared/services/email-content-processor.service';
import { TinyUrlService } from 'src/shared/services/tiny-url.service';
import { AuthModule } from '../auth/auth.module';
import { FileShortLinksModule } from '../file-short-links/file-short-links.module';
import { CampController } from './camp.controller';
import { CampService } from './camp.service';
import { ClientCampController } from './client-camp.controller';
import {
  EmailTemplate,
  EmailTemplateSchema,
} from 'src/shared/entities/camp/email-template.entity';
import {
  EmailWebhookLog,
  EmailWebhookLogSchema,
} from 'src/shared/entities/camp/email-webhook-log.entity';
import {
  EmailQuota,
  EmailQuotaSchema,
} from 'src/shared/entities/email/email-quota.entity';

import { TestWebhookCampController } from './test-webhook-camp.controller';
import { CampaignStageWebhookService } from 'src/shared/services/campaign-stage-webhook.service';
import {
  CampaignStageProgress,
  CampaignStageProgressSchema,
} from 'src/shared/entities/camp/campaign-stage-progress.entity';
import { DubModule } from 'src/shared/services/dub';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Camp.name,
          schema: CampSchema,
        },
        {
          name: CampUser.name,
          schema: CampUserSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
        {
          name: EmailTemplate.name,
          schema: EmailTemplateSchema,
        },
        {
          name: EmailWebhookLog.name,
          schema: EmailWebhookLogSchema,
        },
        {
          name: EmailQuota.name,
          schema: EmailQuotaSchema,
        },

        {
          name: CampaignStageProgress.name,
          schema: CampaignStageProgressSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
    FileShortLinksModule,
    DubModule,
  ],
  controllers: [
    CampController,
    ClientCampController,
    TestWebhookCampController,
  ],
  providers: [
    CampService,
    AzureStorageService,
    CampaignExecutionService,
    EmailService,
    EmailQuotaService,

    CampaignStageWebhookService,
    CampaignStageFlowService,
    EmailContentProcessorService,
    TinyUrlService,
  ],
  exports: [CampService],
})
export class CampsModule {}
