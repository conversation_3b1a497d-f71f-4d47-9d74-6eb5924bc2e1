import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsDate,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  Min,
  Max,
  IsMongoId,
  IsEnum,
} from 'class-validator';
import { CAMP_STATUS } from 'src/shared/constants/camp.constant';

export class EmailTemplateResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  name: string;

  @Expose()
  description?: string;

  @Expose()
  sender: string;

  @Expose()
  senderName: string;

  @Expose()
  brand: string;

  @Expose()
  domain: string;

  @Expose()
  prefix: string;

  @Expose()
  file?: string;

  @Expose()
  requirements?: string[];

  @Expose()
  expirationLink?: string;

  @Expose()
  startFlow: any[];

  @Expose()
  remindFlow: any[];

  @Expose()
  endFlow: any[];

  @Expose()
  periodicFlow: any[];

  @Expose()
  status: string;

  @Expose()
  totalSubscribers: number;

  @Expose()
  totalEmailsSent: number;

  @Expose()
  totalOpened: number;

  @Expose()
  totalClicked: number;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}

export class GetCampResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  brand: string;

  @Expose()
  startDate: string;

  @Expose()
  file: string;

  @Expose()
  @Type(() => EmailTemplateResponseDTO)
  emailTemplate?: EmailTemplateResponseDTO;

  @Expose()
  webhookUrl?: string;

  @ApiProperty({
    description: 'Campaign status',
    enum: CAMP_STATUS,
    example: CAMP_STATUS.DRAFT,
  })
  @Expose()
  status: CAMP_STATUS;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}

export class CreateCampDTO {
  @ApiProperty({
    description: 'Brand name',
    example: 'example.com',
  })
  @IsString()
  @IsNotEmpty()
  brand: string;

  @ApiProperty({
    description: 'Campaign start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsString()
  @IsNotEmpty()
  startDate: string;

  @ApiProperty({
    description: 'Email template ID',
    example: '507f1f77bcf86cd799439012',
  })
  @IsString()
  @IsNotEmpty()
  @IsMongoId({ message: 'emailTemplate must be a valid MongoDB ObjectId' })
  emailTemplate: string;
}

export class UpdateCampDTO {
  @ApiPropertyOptional({
    description: 'Brand name',
    example: 'example.com',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiPropertyOptional({
    description: 'Campaign start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Email template ID',
    example: '507f1f77bcf86cd799439012',
  })
  @IsString()
  @IsOptional()
  @IsMongoId({ message: 'emailTemplate must be a valid MongoDB ObjectId' })
  emailTemplate?: string;

  @ApiPropertyOptional({
    description: 'Campaign status',
    enum: CAMP_STATUS,
    example: CAMP_STATUS.ACTIVE,
  })
  @IsEnum(CAMP_STATUS)
  @IsOptional()
  status?: CAMP_STATUS;
}

export class GetCampQueryDTO {
  @ApiPropertyOptional({
    description: 'Filter by brand name',
    example: 'mybrand.com',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiPropertyOptional({
    description: 'Filter by start date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({
    description: 'Filter by campaign status',
    enum: CAMP_STATUS,
    example: CAMP_STATUS.ACTIVE,
  })
  @IsEnum(CAMP_STATUS)
  @IsOptional()
  status?: CAMP_STATUS;

  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

export class GetCampsResponseDTO {
  @ApiProperty({
    description: 'Array of camps',
    type: [GetCampResponseDTO],
  })
  @Expose()
  @Type(() => GetCampResponseDTO)
  data: GetCampResponseDTO[];

  @ApiProperty({
    description: 'Total number of camps',
    example: 25,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class CampaignExecutionStatusDTO {
  @ApiProperty({
    description: 'Total number of executions',
    example: 150,
  })
  @Expose()
  totalExecutions: number;

  @ApiProperty({
    description: 'Number of pending executions',
    example: 25,
  })
  @Expose()
  pending: number;

  @ApiProperty({
    description: 'Number of running executions',
    example: 5,
  })
  @Expose()
  running: number;

  @ApiProperty({
    description: 'Number of completed executions',
    example: 115,
  })
  @Expose()
  completed: number;

  @ApiProperty({
    description: 'Number of failed executions',
    example: 5,
  })
  @Expose()
  failed: number;

  @ApiProperty({
    description: 'Number of paused executions',
    example: 10,
  })
  @Expose()
  paused: number;

  @ApiProperty({
    description: 'Number of cancelled executions',
    example: 3,
  })
  @Expose()
  cancelled: number;

  @ApiProperty({
    description: 'Completion rate percentage',
    example: 76.7,
  })
  @Expose()
  completionRate: number;
}

export class CampaignOverviewResponseDTO {
  @ApiProperty({
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @Expose()
  campaignId: string;

  @ApiProperty({
    description: 'Campaign name from email campaign',
    example: 'Welcome Series Campaign',
  })
  @Expose()
  campaignName: string;

  @ApiProperty({
    description: 'Campaign status from email campaign',
    example: 'active',
    enum: ['draft', 'active', 'paused', 'completed'],
  })
  @Expose()
  campaignStatus: string;

  @ApiProperty({
    description: 'Brand name',
    example: 'themany',
  })
  @Expose()
  brand: string;

  @ApiProperty({
    description: 'Campaign start date',
    example: '2025-05-23T10:49:54.919Z',
  })
  @Expose()
  startDate: string;

  @ApiProperty({
    description: 'Total number of users in the campaign',
    example: 1000,
  })
  @Expose()
  totalUsers: number;

  @ApiProperty({
    description: 'Total number of emails sent',
    example: 2500,
  })
  @Expose()
  totalEmailsSent: number;

  @ApiProperty({
    description: 'Total number of emails opened',
    example: 1500,
  })
  @Expose()
  totalEmailsOpened: number;

  @ApiProperty({
    description: 'Total number of emails clicked',
    example: 750,
  })
  @Expose()
  totalEmailsClicked: number;

  @ApiProperty({
    description:
      'Success rate percentage (users who received emails successfully)',
    example: 98.5,
  })
  @Expose()
  successRate: number;

  @ApiProperty({
    description: 'Open rate percentage',
    example: 60.0,
  })
  @Expose()
  openRate: number;

  @ApiProperty({
    description: 'Click rate percentage',
    example: 30.0,
  })
  @Expose()
  clickRate: number;

  @ApiProperty({
    description: 'Number of users who opened at least one email',
    example: 850,
  })
  @Expose()
  usersWithOpenedEmails: number;

  @ApiProperty({
    description: 'Total duration of the campaign flows',
    example: '2 days 3 hours',
  })
  @Expose()
  duration: string;

  @ApiProperty({
    description: 'Campaign execution status (if campaign is running)',
    type: CampaignExecutionStatusDTO,
    required: false,
  })
  @Expose()
  @Type(() => CampaignExecutionStatusDTO)
  executionStatus?: CampaignExecutionStatusDTO;
}

export class EmailPerformanceMetricsDTO {
  @ApiProperty({
    description: 'Number of emails sent',
    example: 1000,
  })
  @Expose()
  sent: number;

  @ApiProperty({
    description: 'Number of emails opened',
    example: 600,
  })
  @Expose()
  opened: number;

  @ApiProperty({
    description: 'Open rate percentage',
    example: 60.0,
  })
  @Expose()
  openRate: number;

  @ApiProperty({
    description: 'Number of emails clicked',
    example: 200,
  })
  @Expose()
  clicked: number;

  @ApiProperty({
    description: 'Click rate percentage',
    example: 26.7,
  })
  @Expose()
  clickRate: number;

  @ApiProperty({
    description: 'Number of conversions (only for end email)',
    example: 150,
    required: false,
  })
  @Expose()
  converted?: number;

  @ApiProperty({
    description: 'Conversion rate percentage (only for end email)',
    example: 20.0,
    required: false,
  })
  @Expose()
  conversionRate?: number;
}

export class EmailPerformanceResponseDTO {
  @ApiProperty({
    description: 'Performance metrics for start email',
    type: EmailPerformanceMetricsDTO,
  })
  @Expose()
  @Type(() => EmailPerformanceMetricsDTO)
  start: EmailPerformanceMetricsDTO;

  @ApiProperty({
    description: 'Performance metrics for end email',
    type: EmailPerformanceMetricsDTO,
  })
  @Expose()
  @Type(() => EmailPerformanceMetricsDTO)
  end: EmailPerformanceMetricsDTO;

  // Dynamic properties for additional email types
  [key: string]: EmailPerformanceMetricsDTO | undefined;
}

export class UserSegmentMetricsDTO {
  @ApiProperty({
    description: 'Position/Job title of users in this segment',
    example: 'Performance Marketing Lead',
  })
  @Expose()
  position: string;

  @ApiProperty({
    description: 'Total number of users in this segment',
    example: 300,
  })
  @Expose()
  totalUsers: number;

  @ApiProperty({
    description: 'Open rate percentage for this segment',
    example: 65.0,
  })
  @Expose()
  openRate: number;

  @ApiProperty({
    description: 'Click rate percentage for this segment',
    example: 30.0,
  })
  @Expose()
  clickRate: number;

  @ApiProperty({
    description: 'Conversion rate percentage for this segment',
    example: 25.0,
  })
  @Expose()
  conversionRate: number;
}

export class UserSegmentAnalysisResponseDTO {
  @ApiProperty({
    description: 'Performance metrics grouped by user position/job',
    type: [UserSegmentMetricsDTO],
  })
  @Expose()
  @Type(() => UserSegmentMetricsDTO)
  byPosition: UserSegmentMetricsDTO[];
}

export class TrendDataDTO {
  @ApiProperty({
    description: 'Date in YYYY-MM-DD format',
    example: '2025-05-23',
  })
  @Expose()
  date: string;

  @ApiProperty({
    description: 'Number of opens for this date',
    example: 200,
    required: false,
  })
  @Expose()
  opens?: number;

  @ApiProperty({
    description: 'Number of conversions for this date',
    example: 50,
    required: false,
  })
  @Expose()
  conversions?: number;
}

export class AvgTimeToOpenDTO {
  @ApiProperty({
    description: 'Average time to open start email',
    example: '2 hours',
  })
  @Expose()
  start: string;

  @ApiProperty({
    description: 'Average time to open other_1 email',
    example: '3 hours',
    required: false,
  })
  @Expose()
  'other[0]'?: string;

  @ApiProperty({
    description: 'Average time to open other_2 email',
    example: '4 hours',
    required: false,
  })
  @Expose()
  'other[1]'?: string;

  @ApiProperty({
    description: 'Average time to open other_3 email',
    example: '5 hours',
    required: false,
  })
  @Expose()
  'other[2]'?: string;

  @ApiProperty({
    description: 'Average time to open other_4 email',
    example: '6 hours',
    required: false,
  })
  @Expose()
  'other[3]'?: string;

  @ApiProperty({
    description: 'Average time to open other_5 email',
    example: '7 hours',
    required: false,
  })
  @Expose()
  'other[4]'?: string;

  @ApiProperty({
    description: 'Average time to open end email',
    example: '1 hour',
  })
  @Expose()
  end: string;

  // Dynamic properties for additional email types
  [key: string]: string | undefined;
}

export class TimeAnalysisResponseDTO {
  @ApiProperty({
    description: 'Average time to open emails by type',
    type: AvgTimeToOpenDTO,
  })
  @Expose()
  @Type(() => AvgTimeToOpenDTO)
  avgTimeToOpen: AvgTimeToOpenDTO;

  @ApiProperty({
    description: 'Average time from start to conversion',
    example: '12 hours',
  })
  @Expose()
  avgTimeToConvert: string;

  @ApiProperty({
    description: 'Daily email open trend',
    type: [TrendDataDTO],
  })
  @Expose()
  @Type(() => TrendDataDTO)
  openTrend: TrendDataDTO[];

  @ApiProperty({
    description: 'Daily conversion trend',
    type: [TrendDataDTO],
  })
  @Expose()
  @Type(() => TrendDataDTO)
  conversionTrend: TrendDataDTO[];
}

export class FailureReasonsDTO {
  @ApiProperty({
    description: 'Number of emails failed due to invalid email addresses',
    example: 10,
  })
  @Expose()
  invalidEmail: number;

  @ApiProperty({
    description: 'Number of emails failed due to server errors',
    example: 5,
  })
  @Expose()
  serverError: number;

  @ApiProperty({
    description: 'Number of emails failed due to other reasons',
    example: 5,
  })
  @Expose()
  other: number;

  @ApiProperty({
    description: 'Number of emails bounced back',
    example: 3,
    required: false,
  })
  @Expose()
  bounced?: number;

  @ApiProperty({
    description: 'Number of emails blocked by spam filters',
    example: 2,
    required: false,
  })
  @Expose()
  spamBlocked?: number;
}

export class ErrorAnalysisResponseDTO {
  @ApiProperty({
    description: 'Total number of failed emails',
    example: 20,
  })
  @Expose()
  failedEmails: number;

  @ApiProperty({
    description: 'Breakdown of failure reasons',
    type: FailureReasonsDTO,
  })
  @Expose()
  @Type(() => FailureReasonsDTO)
  failureReasons: FailureReasonsDTO;

  @ApiProperty({
    description: 'Bounce rate percentage',
    example: 2.0,
  })
  @Expose()
  bounceRate: number;

  @ApiProperty({
    description: 'Number of users who never interacted with any email',
    example: 100,
  })
  @Expose()
  nonInteractiveUsers: number;
}
