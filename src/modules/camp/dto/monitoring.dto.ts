import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min, Max, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO for monitoring overview query parameters
 */
export class MonitoringOverviewQueryDTO {
  // No query parameters needed for overview
}

/**
 * DTO for monitoring emails query parameters
 */
export class MonitoringEmailsQueryDTO {
  @ApiPropertyOptional({
    description: 'Pagination offset',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(0)
  skip?: number = 0;

  @ApiPropertyOptional({
    description: 'Items per page (max 100)',
    example: 50,
    default: 50,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 50;

  @ApiPropertyOptional({
    description: 'Filter by status (comma separated)',
    example: 'pending,sent,delivered',
    enum: ['pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed'],
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Search in email/name/subject',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'sentAt',
    enum: ['sentAt', 'email', 'status', 'openedAt', 'clickedAt'],
    default: 'sentAt',
  })
  @IsOptional()
  @IsIn(['sentAt', 'email', 'status', 'openedAt', 'clickedAt'])
  sortBy?: string = 'sentAt';

  @ApiPropertyOptional({
    description: 'Sort direction',
    example: 'desc',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: string = 'desc';
}

/**
 * Campaign overview statistics
 */
export class CampaignOverviewStatsDTO {
  @ApiProperty({ description: 'Total emails in campaign', example: 1250 })
  totalEmails: number;

  @ApiProperty({ description: 'Emails sent', example: 1100 })
  sent: number;

  @ApiProperty({ description: 'Emails delivered', example: 1050 })
  delivered: number;

  @ApiProperty({ description: 'Emails opened', example: 420 })
  opened: number;

  @ApiProperty({ description: 'Emails clicked', example: 89 })
  clicked: number;

  @ApiProperty({ description: 'Emails bounced', example: 25 })
  bounced: number;

  @ApiProperty({ description: 'Emails failed', example: 15 })
  failed: number;

  @ApiProperty({ description: 'Emails pending', example: 150 })
  pending: number;

  @ApiProperty({ description: 'Open rate percentage', example: 38.18 })
  openRate: number;

  @ApiProperty({ description: 'Click rate percentage', example: 8.09 })
  clickRate: number;

  @ApiProperty({ description: 'Bounce rate percentage', example: 2.27 })
  bounceRate: number;

  @ApiProperty({ description: 'Delivery rate percentage', example: 95.45 })
  deliveryRate: number;
}

/**
 * Campaign overview information
 */
export class CampaignOverviewInfoDTO {
  @ApiProperty({ description: 'Campaign ID', example: '507f1f77bcf86cd799439011' })
  id: string;

  @ApiProperty({ description: 'Campaign name', example: 'Summer Sale 2024' })
  name: string;

  @ApiProperty({ description: 'Campaign status', example: 'active' })
  status: string;

  @ApiProperty({ description: 'Campaign start date', example: '2024-12-19T10:00:00.000Z' })
  startDate: string;

  @ApiProperty({ description: 'Campaign created date', example: '2024-12-18T15:30:00.000Z' })
  createdAt: string;

  @ApiProperty({ description: 'Last updated date', example: '2024-12-19T14:27:00.000Z' })
  lastUpdated: string;

  @ApiProperty({ description: 'Email template subject', example: 'Exclusive Summer Deals Just for You!' })
  emailSubject: string;

  @ApiProperty({ description: 'Brand name', example: 'MyBrand' })
  brand: string;

  @ApiProperty({ description: 'Created by username', example: 'admin' })
  createdBy: string;
}

/**
 * Response DTO for monitoring overview API
 */
export class MonitoringOverviewResponseDTO {
  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({ description: 'Campaign information' })
  campaign: CampaignOverviewInfoDTO;

  @ApiProperty({ description: 'Campaign statistics' })
  statistics: CampaignOverviewStatsDTO;

  @ApiProperty({ description: 'Data timestamp', example: '2024-12-19T14:27:00.000Z' })
  timestamp: string;
}

/**
 * Email item in monitoring list
 */
export class MonitoringEmailItemDTO {
  @ApiProperty({ description: 'Email log ID', example: '507f1f77bcf86cd799439012' })
  id: string;

  @ApiProperty({ description: 'Recipient email', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'Recipient name', example: 'John Doe' })
  name: string;

  @ApiProperty({ description: 'Email subject', example: 'Welcome to our service!' })
  subject: string;

  @ApiProperty({ 
    description: 'Email status',
    example: 'delivered',
    enum: ['pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed']
  })
  status: string;

  @ApiProperty({ description: 'Sent timestamp', example: '2024-12-19T10:30:00.000Z' })
  sentAt: string;

  @ApiProperty({ description: 'Opened timestamp', example: '2024-12-19T10:35:00.000Z', nullable: true })
  openedAt: string | null;

  @ApiProperty({ description: 'Clicked timestamp', example: '2024-12-19T10:40:00.000Z', nullable: true })
  clickedAt: string | null;

  @ApiProperty({ description: 'Device used', example: 'iOS' })
  device: string;

  @ApiProperty({ description: 'Location', example: 'Hanoi, VN' })
  location: string;

  @ApiProperty({ description: 'Message ID', example: 'msg_abc123' })
  messageId: string;
}

/**
 * Response DTO for monitoring emails API
 */
export class MonitoringEmailsResponseDTO {
  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({ description: 'Email list', type: [MonitoringEmailItemDTO] })
  data: MonitoringEmailItemDTO[];

  @ApiProperty({ description: 'Total count', example: 1250 })
  total: number;

  @ApiProperty({ description: 'Skip offset', example: 0 })
  skip: number;

  @ApiProperty({ description: 'Limit per page', example: 50 })
  limit: number;

  @ApiProperty({ description: 'Data timestamp', example: '2024-12-19T14:27:00.000Z' })
  timestamp: string;
}

/**
 * Error response DTO
 */
export class MonitoringErrorResponseDTO {
  @ApiProperty({ description: 'Success status', example: false })
  success: boolean;

  @ApiProperty({ description: 'Error code', example: 'CAMPAIGN_NOT_FOUND' })
  error: string;

  @ApiProperty({ description: 'Error message', example: 'Campaign not found' })
  message: string;

  @ApiProperty({ description: 'Error timestamp', example: '2024-12-19T14:27:00.000Z' })
  timestamp: string;
}
