import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { plainToInstance } from 'class-transformer';
import { get } from 'lodash';
import csv from 'csv-parser';
import { Readable } from 'stream';
import * as geoip from 'geoip-lite';
import { AdminDB } from 'src/config';
import { SYSTEM } from 'src/shared/constants/common.constant';
import { User } from 'src/shared/entities/auth/user.entity';
import { Camp, CampDocument } from 'src/shared/entities/camp/camp.entity';
import {
  CampUser,
  CampUserDocument,
} from 'src/shared/entities/camp/camp-user.entity';
import {
  EMAIL_STATUS,
  CAMP_USER_STATUS,
  EMAIL_TYPE,
} from 'src/shared/constants/camp.constant';

import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { CampaignExecutionService } from 'src/shared/services/campaign-execution.service';

import { CampaignStageFlowService } from 'src/shared/services/campaign-stage-flow.service';
import { CampaignStageWebhookService } from 'src/shared/services/campaign-stage-webhook.service';
import { extractSubdomain } from 'src/shared/utils/common.util';
import { CAMP_STATUS } from 'src/shared/constants/camp.constant';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import {
  CreateCampDTO,
  GetCampQueryDTO,
  GetCampResponseDTO,
  GetCampsResponseDTO,
  UpdateCampDTO,
  CampaignOverviewResponseDTO,
  EmailPerformanceResponseDTO,
  EmailPerformanceMetricsDTO,
  UserSegmentAnalysisResponseDTO,
  TimeAnalysisResponseDTO,
  TrendDataDTO,
  ErrorAnalysisResponseDTO,
} from './dto/camp.dto';
import {
  UpdateEmailTrackingDTO,
  CampUserResponseDTO,
} from './dto/camp-user.dto';
import {
  MonitoringEmailsQueryDTO,
  MonitoringOverviewResponseDTO,
  MonitoringEmailsResponseDTO,
  CampaignOverviewStatsDTO,
  CampaignOverviewInfoDTO,
  MonitoringEmailItemDTO,
} from './dto/monitoring.dto';
import {
  EmailTemplate,
  EmailTemplateDocument,
} from 'src/shared/entities/camp/email-template.entity';
import {
  EmailWebhookLog,
  EmailWebhookLogDocument,
} from 'src/shared/entities/camp/email-webhook-log.entity';

@Injectable()
export class CampService {
  private readonly logger = new Logger(CampService.name);

  constructor(
    @InjectModel(Camp.name, AdminDB)
    private readonly campModel: AuthModel<CampDocument>,
    @InjectModel(CampUser.name, AdminDB)
    private readonly campUserModel: AuthModel<CampUserDocument>,
    @InjectModel(EmailTemplate.name, AdminDB)
    private readonly emailTemplateModel: AuthModel<EmailTemplateDocument>,
    @InjectModel(EmailWebhookLog.name, AdminDB)
    private readonly emailWebhookLogModel: Model<EmailWebhookLogDocument>,

    private readonly azureStorageService: AzureStorageService,
    private readonly campaignExecutionService: CampaignExecutionService,
    private readonly campaignStageFlowService: CampaignStageFlowService,
    private readonly campaignStageWebhookService: CampaignStageWebhookService,
  ) {}

  async getCamps(
    getCampsQueryDTO: GetCampQueryDTO,
  ): Promise<GetCampsResponseDTO> {
    const { skip = 0, limit = 10, brand, startDate, status } = getCampsQueryDTO;

    const query: Record<string, any> = {};
    if (brand) {
      query.brand = extractSubdomain(brand);
    }
    if (startDate) {
      query.startDate = {
        $gte: new Date(startDate),
      };
    }
    if (status) {
      query.status = status;
    }

    const [camps, total] = await Promise.all([
      this.campModel
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 })
        .lean(),
      this.campModel.countDocuments(query),
    ]);

    const data = camps.map((camp) =>
      plainToInstance(GetCampResponseDTO, camp, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async getCampDetail(id: string): Promise<GetCampResponseDTO> {
    try {
      const camp = await this.campModel
        .findOne({ _id: id })
        .populate('emailTemplate')
        .lean()
        .exec();

      if (!camp) {
        throw new BadRequestException('Camp not found');
      }

      return plainToInstance(GetCampResponseDTO, camp, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.error('Error fetching camp detail:', error);
      throw new BadRequestException('Failed to get camp detail');
    }
  }

  async createCamp(
    createCampDTO: CreateCampDTO,
    file: Express.Multer.File,
    authUser?: User,
  ): Promise<GetCampResponseDTO> {
    const { brand, startDate } = createCampDTO;

    if (!file) {
      throw new BadRequestException('File is required');
    }

    const fileUrl = await this.azureStorageService.uploadFile(file);

    const newCamp = new this.campModel({
      brand: extractSubdomain(brand),
      startDate: new Date(startDate),
      file: fileUrl,
      emailTemplate: createCampDTO.emailTemplate,
      createdAt: new Date(),
      createdBy: authUser ? authUser.telegramId : SYSTEM,
      createdByUsername: authUser ? authUser.username : 'system',
    });
    const savedCamp = await newCamp.save();

    // Generate webhook URL tied to camp ID
    const webhookUrl = `${process.env.API_BASE_URL || 'http://localhost:3000'}/api/camps/${savedCamp._id}/webhook`;

    // Update camp with webhook URL
    await this.campModel.findByIdAndUpdate(savedCamp._id, {
      webhookUrl: webhookUrl,
    });

    // Auto sync users from CSV file after creating campaign
    try {
      await this.syncUsersFromFile(
        (savedCamp._id as any).toString(),
        file,
        authUser || ({ telegramId: SYSTEM } as User),
      );
    } catch (error) {
      console.error('Failed to sync users after creating campaign:', error);
    }

    return plainToInstance(GetCampResponseDTO, savedCamp, {
      excludeExtraneousValues: true,
    });
  }

  async updateCamp(
    id: string,
    updateCampDTO: UpdateCampDTO,
    authUser: User,
    file?: Express.Multer.File,
  ): Promise<GetCampResponseDTO> {
    const { brand, startDate, emailTemplate } = updateCampDTO;

    const camp = await this.campModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean();

    if (!camp) {
      throw new BadRequestException('Camp not found');
    }

    let fileUrl = get(camp, 'file', '');
    if (file) {
      fileUrl = await this.azureStorageService.uploadFile(file);
    }

    const bodyUpdate: Partial<CampDocument> = {};

    if (brand) {
      bodyUpdate.brand = extractSubdomain(brand);
    }
    if (startDate) {
      bodyUpdate.startDate = new Date(startDate);
    }
    if (fileUrl) {
      bodyUpdate.file = fileUrl;
    }
    if (emailTemplate) {
      bodyUpdate.emailTemplate = emailTemplate;
    }

    const updatedCamp = await this.campModel.findOneAndUpdateWithAuth(
      {
        _id: id,
      },
      {
        ...bodyUpdate,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      },
      { new: true },
      authUser,
    );

    // Auto sync users from CSV file if new file is uploaded
    if (file) {
      try {
        await this.syncUsersFromFile(id, file, authUser);
      } catch (error) {
        console.error('Failed to sync users after updating campaign:', error);
      }
    }

    return plainToInstance(GetCampResponseDTO, updatedCamp, {
      excludeExtraneousValues: true,
    });
  }

  async deleteCamp(id: string, authUser: User): Promise<{ success: boolean }> {
    const camp = await this.campModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean()
      .exec();

    if (!camp) {
      throw new BadRequestException('Camp not found');
    }

    await this.campModel
      .findByIdAndUpdate(id, {
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
        deletedAt: new Date(),
        deletedBy: authUser.telegramId,
      })
      .lean()
      .exec();

    return { success: true };
  }

  /**
   * Handle webhook from AWS SES
   */
  async handleEmailWebhook(
    webhookPayload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Determine webhook type and process accordingly
      if (this.isAwsEventBridgeWebhook(webhookPayload)) {
        // AWS EventBridge webhook (preferred format)
        const result = await this.processEventBridgeWebhook(webhookPayload);
        return result;
      } else if (this.isAwsSesWebhook(webhookPayload)) {
        // AWS SES webhook (SNS format) - Legacy support
        const result = await this.processSnsSesWebhook(webhookPayload);
        return result;
      } else {
        this.logger.warn(
          `Unknown webhook format received, logging for analysis`,
        );
        const result = await this.logUnknownWebhook(webhookPayload);
        return result;
      }
    } catch (error) {
      this.logger.error(`Failed to process AWS webhook:`, error);
      throw new BadRequestException(
        `Failed to process webhook: ${error.message}`,
      );
    }
  }

  /**
   * Check if webhook is AWS SES format (SNS notification)
   */
  private isAwsSesWebhook(payload: any): boolean {
    return payload.Type && payload.Message && payload.TopicArn;
  }

  /**
   * Check if webhook is AWS EventBridge format
   */
  private isAwsEventBridgeWebhook(payload: any): boolean {
    return payload.version && payload.source && payload['detail-type'];
  }

  /**
   * Process AWS SNS SES webhook (Legacy support)
   */
  private async processSnsSesWebhook(
    payload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Parse SNS message
      let eventMessage: any;
      try {
        eventMessage = JSON.parse(payload.Message);
      } catch (error) {
        this.logger.error('Failed to parse SNS webhook message:', error);
        throw new Error('Invalid SNS webhook message format');
      }

      // Extract camp ID from message
      const campId = this.extractCampIdFromSnsMessage(eventMessage);
      if (!campId) {
        this.logger.warn('No camp ID found in SNS webhook message');
        return { success: true, message: 'No camp ID found in SNS webhook' };
      }

      const email = eventMessage.mail?.destination?.[0];
      if (!email) {
        this.logger.warn('No destination email found in SNS webhook');
        return { success: true, message: 'No destination email found' };
      }

      // Create webhook log
      await this.createSnsWebhookLog(campId, email, eventMessage, payload);

      // Update email tracking
      await this.updateEmailTrackingFromSns(campId, email, eventMessage);

      return {
        success: true,
        message: `SNS SES ${eventMessage.eventType} event processed successfully`,
      };
    } catch (error) {
      this.logger.error('Failed to process SNS SES webhook:', error);
      throw error;
    }
  }

  /**
   * Process AWS EventBridge webhook
   */
  private async processEventBridgeWebhook(
    payload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if this is an AWS SES event
      if (payload.source === 'aws.ses' && payload.detail) {
        return await this.processEventBridgeSesEvent(payload);
      }

      // For other EventBridge events, return success
      return {
        success: true,
        message: 'EventBridge event processed (non-SES)',
      };
    } catch (error) {
      this.logger.error('Failed to process EventBridge webhook:', error);
      throw error;
    }
  }

  /**
   * Process AWS SES events from EventBridge
   */
  private async processEventBridgeSesEvent(
    payload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const detail = payload.detail;
      const eventType = detail.eventType;
      const mail = detail.mail;

      if (!mail || !mail.destination || mail.destination.length === 0) {
        this.logger.warn('No destination email found in EventBridge SES event');
        return { success: true, message: 'No destination email found' };
      }

      // Extract camp ID from email tags
      const campId = this.extractCampIdFromEventBridge(detail);
      if (!campId) {
        this.logger.warn('No camp ID found in EventBridge SES event');
        return { success: true, message: 'No camp ID found' };
      }

      const email = mail.destination[0];

      // Create webhook log
      await this.createEventBridgeWebhookLog(campId, email, detail, payload);

      // Update email tracking based on event type
      await this.updateEmailTrackingFromEventBridge(campId, email, detail);

      return {
        success: true,
        message: `EventBridge SES ${eventType} event processed successfully`,
      };
    } catch (error) {
      this.logger.error('Failed to process EventBridge SES event:', error);
      throw error;
    }
  }

  /**
   * Extract camp ID from EventBridge SES event
   */
  private extractCampIdFromEventBridge(detail: any): string | null {
    try {
      // Try to extract from mail tags
      if (detail.mail?.tags?.campaign) {
        const campaignTag = Array.isArray(detail.mail.tags.campaign)
          ? detail.mail.tags.campaign[0]
          : detail.mail.tags.campaign;

        if (campaignTag && campaignTag.startsWith('camp_')) {
          return campaignTag.replace('camp_', '');
        }
      }

      // Try to extract from headers
      if (detail.mail?.headers) {
        for (const header of detail.mail.headers) {
          if (header.name === 'X-Ses-Message-Tags' && header.value) {
            const match = header.value.match(/campaign=camp_([a-f0-9]+)/);
            if (match) {
              return match[1];
            }
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Error extracting camp ID from EventBridge:', error);
      return null;
    }
  }

  /**
   * Create webhook log for EventBridge SES event
   */
  private async createEventBridgeWebhookLog(
    campId: string,
    email: string,
    detail: any,
    payload: any,
  ): Promise<void> {
    try {
      const webhookLog = new this.emailWebhookLogModel({
        campId,
        email,
        emailType: 'unknown', // Will be determined from campaign stage
        status: detail.eventType?.toLowerCase() || 'unknown',
        messageId: detail.mail?.messageId,
        subject: detail.mail?.commonHeaders?.subject,
        timestamp: new Date(
          detail.open?.timestamp ||
            detail.click?.timestamp ||
            detail.mail?.timestamp ||
            payload.time,
        ),
        userAgent: detail.open?.userAgent || detail.click?.userAgent,
        ipAddress: detail.open?.ipAddress || detail.click?.ipAddress,
        clickedUrl: detail.click?.link,
        rawPayload: payload,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await webhookLog.save();
    } catch (error) {
      this.logger.error('Failed to create EventBridge webhook log:', error);
    }
  }

  /**
   * Update email tracking from EventBridge SES event
   */
  private async updateEmailTrackingFromEventBridge(
    campId: string,
    email: string,
    detail: any,
  ): Promise<void> {
    try {
      const campUser = await this.campUserModel.findOne({
        campId,
        email,
      });

      if (!campUser) {
        this.logger.warn(`Camp user not found: ${email} in camp ${campId}`);
        return;
      }

      const eventType = detail.eventType;
      const timestamp = new Date(
        detail.open?.timestamp ||
          detail.click?.timestamp ||
          detail.mail?.timestamp ||
          new Date(),
      );

      // Find the most recent email tracking entry to update
      let emailTrack = campUser.emailTracking
        .filter((track) => track.messageId === detail.mail?.messageId)
        .pop();

      if (!emailTrack && campUser.emailTracking.length > 0) {
        // If no messageId match, use the most recent tracking entry
        emailTrack = campUser.emailTracking[campUser.emailTracking.length - 1];
      }

      if (!emailTrack) {
        // Try to determine email type from campaign stage progress
        const emailType = await this.determineEmailTypeFromCampaignStage(
          campId,
          email,
        );

        // Create new tracking entry if none exists
        emailTrack = {
          emailType: emailType || 'unknown',
          status: EMAIL_STATUS.PENDING,
          openCount: 0,
          clickCount: 0,
          messageId: detail.mail?.messageId,
        };
        campUser.emailTracking.push(emailTrack);
      }

      // Update tracking based on event type
      switch (eventType) {
        case 'Send':
          emailTrack.status = EMAIL_STATUS.SENT;
          emailTrack.sentAt = timestamp;
          campUser.lastEmailSentAt = timestamp;
          break;

        case 'Delivery':
          emailTrack.status = EMAIL_STATUS.DELIVERED;
          emailTrack.deliveredAt = timestamp;
          break;

        case 'Open':
          emailTrack.status = EMAIL_STATUS.OPENED;
          emailTrack.openedAt = timestamp;
          emailTrack.openCount = (emailTrack.openCount || 0) + 1;

          // ✅ IMPORTANT: Notify campaign stage flow about email open
          await this.campaignStageWebhookService.handleEmailOpen(
            campId,
            email,
            detail.mail?.messageId,
          );
          break;

        case 'Click':
          emailTrack.status = EMAIL_STATUS.CLICKED;
          emailTrack.clickedAt = timestamp;
          emailTrack.clickCount = (emailTrack.clickCount || 0) + 1;

          // ✅ IMPORTANT: Notify campaign stage flow about email click
          await this.campaignStageWebhookService.handleEmailClick(
            campId,
            email,
            detail.mail?.messageId,
            detail.click?.link,
          );
          break;

        case 'Bounce':
          emailTrack.status = EMAIL_STATUS.BOUNCED;
          emailTrack.bouncedAt = timestamp;
          emailTrack.failureReason = detail.bounce?.bounceType || 'Bounced';
          break;

        case 'Complaint':
        case 'Reject':
          emailTrack.status = EMAIL_STATUS.FAILED;
          emailTrack.failedAt = timestamp;
          emailTrack.failureReason =
            detail.complaint?.complaintFeedbackType ||
            detail.reject?.reason ||
            'Failed';
          break;
      }

      // Update messageId if not set
      if (!emailTrack.messageId && detail.mail?.messageId) {
        emailTrack.messageId = detail.mail.messageId;
      }

      campUser.updatedAt = new Date();
      await campUser.save();
    } catch (error) {
      this.logger.error(
        'Failed to update email tracking from EventBridge:',
        error,
      );
    }
  }

  /**
   * Determine email type from campaign stage progress
   */
  private async determineEmailTypeFromCampaignStage(
    campId: string,
    email: string,
  ): Promise<string | null> {
    try {
      // Try to find campaign stage progress for this user using CampaignStageProgress model
      const CampaignStageProgress =
        this.campaignStageFlowService['campaignStageProgressModel'];
      if (CampaignStageProgress) {
        const progress = await CampaignStageProgress.findOne({
          campId,
          email,
        }).lean();

        if (progress) {
          return progress.currentStage || null;
        }
      }

      // Fallback: assume it's initiation stage for new users
      return 'initiation';
    } catch (error) {
      this.logger.error(
        'Failed to determine email type from campaign stage:',
        error,
      );
      return null;
    }
  }

  /**
   * Check if campaign status allows starting
   */
  private validateCampaignStatusForStart(currentStatus: string): void {
    if (currentStatus === CAMP_STATUS.ACTIVE) {
      throw new BadRequestException(
        'Campaign is already running. Please stop the current campaign before starting a new one.',
      );
    }

    if (currentStatus === CAMP_STATUS.PAUSED) {
      throw new BadRequestException(
        'Campaign is currently paused. Please use resume-campaign endpoint to continue the existing campaign.',
      );
    }

    // Only allow starting from DRAFT, COMPLETED, or CANCELLED states
    const allowedStatuses = [
      CAMP_STATUS.DRAFT,
      CAMP_STATUS.COMPLETED,
      CAMP_STATUS.CANCELLED,
    ];
    if (!allowedStatuses.includes(currentStatus as CAMP_STATUS)) {
      throw new BadRequestException(
        `Cannot start campaign from current status: ${currentStatus}. Campaign must be in draft, completed, or cancelled state.`,
      );
    }
  }

  /**
   * Check if campaign status allows pausing
   */
  private validateCampaignStatusForPause(currentStatus: string): void {
    if (currentStatus !== CAMP_STATUS.ACTIVE) {
      throw new BadRequestException(
        `Cannot pause campaign. Campaign must be in active state, current status: ${currentStatus}`,
      );
    }
  }

  /**
   * Check if campaign status allows resuming
   */
  private validateCampaignStatusForResume(currentStatus: string): void {
    if (currentStatus !== CAMP_STATUS.PAUSED) {
      throw new BadRequestException(
        `Cannot resume campaign. Campaign must be in paused state, current status: ${currentStatus}`,
      );
    }
  }

  /**
   * Check if campaign status allows stopping
   */
  private validateCampaignStatusForStop(currentStatus: string): void {
    const allowedStatuses = [CAMP_STATUS.ACTIVE, CAMP_STATUS.PAUSED];
    if (!allowedStatuses.includes(currentStatus as CAMP_STATUS)) {
      throw new BadRequestException(
        `Cannot stop campaign. Campaign must be in active or paused state, current status: ${currentStatus}`,
      );
    }
  }

  /**
   * Extract camp ID from SNS message
   */
  private extractCampIdFromSnsMessage(eventMessage: any): string | null {
    try {
      // Try to extract from tags first
      if (eventMessage.mail?.tags?.campId) {
        return Array.isArray(eventMessage.mail.tags.campId)
          ? eventMessage.mail.tags.campId[0]
          : eventMessage.mail.tags.campId;
      }

      // Try to extract from headers
      if (eventMessage.mail?.headers) {
        for (const header of eventMessage.mail.headers) {
          if (header.name?.toLowerCase() === 'x-camp-id') {
            return header.value;
          }
          if (header.name?.toLowerCase() === 'x-campaign-id') {
            return header.value;
          }
          if (header.name === 'X-Ses-Message-Tags' && header.value) {
            const match = header.value.match(/campaign=camp_([a-f0-9]+)/);
            if (match) {
              return match[1];
            }
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.error('Error extracting camp ID from SNS message:', error);
      return null;
    }
  }

  /**
   * Create webhook log for SNS SES event
   */
  private async createSnsWebhookLog(
    campId: string,
    email: string,
    eventMessage: any,
    payload: any,
  ): Promise<void> {
    try {
      const webhookLog = new this.emailWebhookLogModel({
        campId,
        email,
        emailType: 'unknown', // Will be determined from campaign stage
        status: this.mapEventTypeToStatus(eventMessage.eventType),
        messageId: eventMessage.mail?.messageId,
        subject: eventMessage.mail?.commonHeaders?.subject,
        timestamp: new Date(
          eventMessage.open?.timestamp ||
            eventMessage.click?.timestamp ||
            eventMessage.delivery?.timestamp ||
            eventMessage.bounce?.timestamp ||
            eventMessage.complaint?.timestamp ||
            eventMessage.mail?.timestamp ||
            new Date(),
        ),
        userAgent:
          eventMessage.open?.userAgent || eventMessage.click?.userAgent,
        ipAddress:
          eventMessage.open?.ipAddress || eventMessage.click?.ipAddress,
        clickedUrl: eventMessage.click?.link,
        errorMessage: eventMessage.reject?.reason,
        bounceReason: eventMessage.bounce?.bounceType,
        rawPayload: payload,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await webhookLog.save();
    } catch (error) {
      this.logger.error('Failed to create SNS webhook log:', error);
    }
  }

  /**
   * Update email tracking from SNS SES event
   */
  private async updateEmailTrackingFromSns(
    campId: string,
    email: string,
    eventMessage: any,
  ): Promise<void> {
    try {
      const campUser = await this.campUserModel.findOne({
        campId,
        email,
      });

      if (!campUser) {
        this.logger.warn(`Camp user not found: ${email} in camp ${campId}`);
        return;
      }

      const eventType = eventMessage.eventType;
      const timestamp = new Date(
        eventMessage.open?.timestamp ||
          eventMessage.click?.timestamp ||
          eventMessage.delivery?.timestamp ||
          eventMessage.bounce?.timestamp ||
          eventMessage.complaint?.timestamp ||
          eventMessage.mail?.timestamp ||
          new Date(),
      );

      // Find the most recent email tracking entry to update
      let emailTrack = campUser.emailTracking
        .filter((track) => track.messageId === eventMessage.mail?.messageId)
        .pop();

      if (!emailTrack && campUser.emailTracking.length > 0) {
        // If no messageId match, use the most recent tracking entry
        emailTrack = campUser.emailTracking[campUser.emailTracking.length - 1];
      }

      if (!emailTrack) {
        // Create new tracking entry if none exists
        emailTrack = {
          emailType: 'unknown',
          status: EMAIL_STATUS.PENDING,
          openCount: 0,
          clickCount: 0,
          messageId: eventMessage.mail?.messageId,
        };
        campUser.emailTracking.push(emailTrack);
      }

      // Update tracking based on event type
      switch (eventType) {
        case 'send':
          emailTrack.status = EMAIL_STATUS.SENT;
          emailTrack.sentAt = timestamp;
          campUser.lastEmailSentAt = timestamp;
          break;

        case 'delivery':
          emailTrack.status = EMAIL_STATUS.DELIVERED;
          emailTrack.deliveredAt = timestamp;
          break;

        case 'open':
          emailTrack.status = EMAIL_STATUS.OPENED;
          emailTrack.openedAt = timestamp;
          emailTrack.openCount = (emailTrack.openCount || 0) + 1;

          // Notify campaign stage flow about email open
          await this.campaignStageWebhookService.handleEmailOpen(
            campId,
            email,
            eventMessage.mail?.messageId,
          );
          break;

        case 'click':
          emailTrack.status = EMAIL_STATUS.CLICKED;
          emailTrack.clickedAt = timestamp;
          emailTrack.clickCount = (emailTrack.clickCount || 0) + 1;

          // Notify campaign stage flow about email click
          await this.campaignStageWebhookService.handleEmailClick(
            campId,
            email,
            eventMessage.mail?.messageId,
            eventMessage.click?.link,
          );
          break;

        case 'bounce':
          emailTrack.status = EMAIL_STATUS.BOUNCED;
          emailTrack.bouncedAt = timestamp;
          emailTrack.failureReason =
            eventMessage.bounce?.bounceType || 'Bounced';
          break;

        case 'complaint':
        case 'reject':
          emailTrack.status = EMAIL_STATUS.FAILED;
          emailTrack.failedAt = timestamp;
          emailTrack.failureReason =
            eventMessage.complaint?.complaintFeedbackType ||
            eventMessage.reject?.reason ||
            'Failed';
          break;
      }

      // Update messageId if not set
      if (!emailTrack.messageId && eventMessage.mail?.messageId) {
        emailTrack.messageId = eventMessage.mail.messageId;
      }

      campUser.updatedAt = new Date();
      await campUser.save();
    } catch (error) {
      this.logger.error('Failed to update email tracking from SNS:', error);
    }
  }

  /**
   * Log unknown webhook format for analysis
   */
  private async logUnknownWebhook(
    payload: any,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Extract any potential message ID
      const messageId =
        payload.id ||
        payload.messageId ||
        payload.MessageId ||
        payload.detail?.uuid ||
        'unknown';

      return {
        success: true,
        message: `Unknown webhook logged successfully. ID: ${messageId}`,
      };
    } catch (error) {
      this.logger.error('Failed to log unknown webhook:', error);
      return {
        success: true, // Return success to avoid AWS retries
        message: 'Webhook received but failed to log',
      };
    }
  }

  async downloadFile(
    id: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const camp = await this.campModel.findById(id);
      if (!camp) {
        throw new BadRequestException('Camp not found');
      }

      return this.azureStorageService.downloadFile(camp.file);
    } catch (error) {
      throw new BadRequestException('Failed to download file', error.message);
    }
  }

  async getCampaignOverview(
    campaignId: string,
  ): Promise<CampaignOverviewResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const totalUsers = await this.campUserModel.countDocuments({
      campId: campaignId,
    });

    // Calculate total planned emails based on users × email templates
    let totalPlannedEmails = 0;
    let campaignName = 'Unknown Campaign';
    let campaignStatus = 'draft';

    if ((camp as any).emailTemplate) {
      const emailTemplate = await this.emailTemplateModel
        .findById((camp as any).emailTemplate)
        .lean()
        .exec();

      if (emailTemplate) {
        campaignName = (emailTemplate as any).name;
        campaignStatus = (emailTemplate as any).status || 'draft';

        // Count total emails in all flows
        const initiationEmails = (
          (emailTemplate as any).startFlow || []
        ).filter((item: any) => item.type === 'email').length;

        const engagementEmails = (
          (emailTemplate as any).remindFlow || []
        ).filter((item: any) => item.type === 'email').length;

        const conversionEmails = ((emailTemplate as any).endFlow || []).filter(
          (item: any) => item.type === 'email',
        ).length;

        const retentionEmails = (
          (emailTemplate as any).periodicFlow || []
        ).filter((item: any) => item.type === 'email').length;

        // Total emails per user (all stages)
        const emailsPerUser =
          initiationEmails +
          engagementEmails +
          conversionEmails +
          retentionEmails;

        // Total planned emails = users × emails per user
        totalPlannedEmails = totalUsers * emailsPerUser;
      }
    }

    const emailStats = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: null,
          totalEmailsSent: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$emailTracking', null] },
                    {
                      $in: [
                        '$emailTracking.status',
                        [
                          EMAIL_STATUS.SENT,
                          EMAIL_STATUS.DELIVERED,
                          EMAIL_STATUS.OPENED,
                          EMAIL_STATUS.CLICKED,
                        ],
                      ],
                    },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalEmailsOpened: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$emailTracking', null] },
                    {
                      $in: [
                        '$emailTracking.status',
                        [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                      ],
                    },
                  ],
                },
                1,
                0,
              ],
            },
          },
          totalEmailsClicked: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$emailTracking', null] },
                    { $eq: ['$emailTracking.status', EMAIL_STATUS.CLICKED] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          successfulUsers: {
            $addToSet: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$emailTracking', null] },
                    {
                      $in: [
                        '$emailTracking.status',
                        [
                          EMAIL_STATUS.DELIVERED,
                          EMAIL_STATUS.OPENED,
                          EMAIL_STATUS.CLICKED,
                        ],
                      ],
                    },
                  ],
                },
                '$email', // Use email instead of userId for uniqueness
                null,
              ],
            },
          },
          usersWithOpenedEmails: {
            $addToSet: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$emailTracking', null] },
                    {
                      $in: [
                        '$emailTracking.status',
                        [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                      ],
                    },
                  ],
                },
                '$email',
                null,
              ],
            },
          },
        },
      },
      {
        $project: {
          totalEmailsSent: 1,
          totalEmailsOpened: 1,
          totalEmailsClicked: 1,
          successfulUsersCount: {
            $size: {
              $filter: {
                input: '$successfulUsers',
                cond: { $ne: ['$$this', null] },
              },
            },
          },
          usersWithOpenedEmailsCount: {
            $size: {
              $filter: {
                input: '$usersWithOpenedEmails',
                cond: { $ne: ['$$this', null] },
              },
            },
          },
        },
      },
    ]);

    const stats = emailStats[0] || {
      totalEmailsSent: 0,
      totalEmailsOpened: 0,
      totalEmailsClicked: 0,
      successfulUsersCount: 0,
      usersWithOpenedEmailsCount: 0,
    };

    const totalEmailsSent = stats.totalEmailsSent;
    const totalEmailsOpened = stats.totalEmailsOpened;
    const totalEmailsClicked = stats.totalEmailsClicked;
    const successfulUsers = stats.successfulUsersCount;
    const usersWithOpenedEmails = stats.usersWithOpenedEmailsCount;

    // Calculate success rate based on users who received emails successfully
    const successRate =
      totalUsers > 0 ? (successfulUsers / totalUsers) * 100 : 0;

    // Calculate open rate and click rate based on planned emails
    const openRate =
      totalPlannedEmails > 0
        ? (totalEmailsOpened / totalPlannedEmails) * 100
        : 0;
    const clickRate =
      totalPlannedEmails > 0
        ? (totalEmailsClicked / totalPlannedEmails) * 100
        : 0;

    // Calculate total duration from email campaign flows
    let totalDuration = 0;

    if ((camp as any).emailTemplate) {
      const emailTemplate = await this.emailTemplateModel
        .findById((camp as any).emailTemplate)
        .lean()
        .exec();

      if (emailTemplate) {
        // Calculate duration from all flows
        const flows = [
          ...((emailTemplate as any).startFlow || []),
          ...((emailTemplate as any).remindFlow || []),
          ...((emailTemplate as any).endFlow || []),
          ...((emailTemplate as any).periodicFlow || []),
        ];

        // Sum up all delay items
        flows.forEach((flowItem: any) => {
          if (flowItem.type === 'delay' && flowItem.delay) {
            totalDuration += flowItem.delay;
          }
        });
      }
    }

    const durationString = this.formatDuration(totalDuration);

    // Get campaign execution status
    let executionStatus: any = null;
    try {
      executionStatus =
        await this.campaignExecutionService.getCampaignExecutionStatus(
          campaignId,
        );
    } catch (error: any) {
      // If execution service fails, continue without execution status
      console.warn(
        `Failed to get execution status for campaign ${campaignId}:`,
        error.message,
      );
    }

    const responseData: any = {
      campaignId: camp._id.toString(),
      campaignName,
      campaignStatus,
      brand: camp.brand,
      startDate: camp.startDate?.toISOString() || new Date().toISOString(),
      totalUsers,
      totalEmails: totalPlannedEmails, // Total planned emails (users × emails per user)
      totalEmailsSent, // Actual emails sent so far
      totalEmailsOpened,
      totalEmailsClicked,
      successRate: Math.round(successRate * 10) / 10,
      openRate: Math.round(openRate * 10) / 10,
      clickRate: Math.round(clickRate * 10) / 10,
      usersWithOpenedEmails,
      duration: durationString,
    };

    // Add execution status if available
    if (executionStatus) {
      responseData.executionStatus = {
        totalExecutions: executionStatus.totalExecutions,
        pending: executionStatus.pending,
        running: executionStatus.running,
        completed: executionStatus.completed,
        failed: executionStatus.failed,
        completionRate:
          executionStatus.totalExecutions > 0
            ? Math.round(
                (executionStatus.completed / executionStatus.totalExecutions) *
                  1000,
              ) / 10
            : 0,
      };
    }

    return plainToInstance(CampaignOverviewResponseDTO, responseData, {
      excludeExtraneousValues: true,
    });
  }

  async updateEmailTracking(
    email: string,
    campId: string,
    updateDto: UpdateEmailTrackingDTO,
  ): Promise<CampUserResponseDTO> {
    const campUser = await this.campUserModel.findOne({ email, campId });
    if (!campUser) {
      throw new BadRequestException('User not found in campaign');
    }

    let emailTrack = campUser.emailTracking.find(
      (track) => track.emailType === updateDto.emailType,
    );

    if (!emailTrack) {
      emailTrack = {
        emailType: updateDto.emailType,
        status: updateDto.status,
        openCount: 0,
        clickCount: 0,
      };
      campUser.emailTracking.push(emailTrack);
    } else {
      emailTrack.status = updateDto.status;
    }

    const timestamp = updateDto.timestamp || new Date();
    switch (updateDto.status) {
      case EMAIL_STATUS.SENT:
        emailTrack.sentAt = timestamp;
        campUser.lastEmailSentAt = timestamp;
        break;
      case EMAIL_STATUS.DELIVERED:
        emailTrack.deliveredAt = timestamp;
        break;
      case EMAIL_STATUS.OPENED:
        emailTrack.openedAt = timestamp;
        emailTrack.openCount += 1;
        break;
      case EMAIL_STATUS.CLICKED:
        emailTrack.clickedAt = timestamp;
        emailTrack.clickCount += 1;
        break;
      case EMAIL_STATUS.BOUNCED:
        emailTrack.bouncedAt = timestamp;
        break;
      case EMAIL_STATUS.FAILED:
        emailTrack.failedAt = timestamp;
        break;
    }

    campUser.updatedAt = new Date();
    const updatedUser = await campUser.save();

    return plainToInstance(CampUserResponseDTO, updatedUser.toObject(), {
      excludeExtraneousValues: true,
    });
  }

  async getEmailPerformance(
    campaignId: string,
  ): Promise<EmailPerformanceResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const performanceData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: '$emailTracking.emailType',
          sent: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [
                      EMAIL_STATUS.SENT,
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                1,
                0,
              ],
            },
          },
          opened: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
                1,
                0,
              ],
            },
          },
          clicked: {
            $sum: {
              $cond: [
                { $eq: ['$emailTracking.status', EMAIL_STATUS.CLICKED] },
                1,
                0,
              ],
            },
          },
        },
      },
    ]);

    const conversionData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
        },
      },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.emailType': EMAIL_TYPE.END,
          'emailTracking.status': {
            $in: [
              EMAIL_STATUS.SENT,
              EMAIL_STATUS.DELIVERED,
              EMAIL_STATUS.OPENED,
              EMAIL_STATUS.CLICKED,
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          converted: { $sum: 1 },
        },
      },
    ]);

    const converted = conversionData[0]?.converted || 0;

    return this.buildEmailPerformanceResponse(performanceData, converted);
  }

  private buildEmailPerformanceResponse(
    performanceData: any[],
    converted: number,
  ): EmailPerformanceResponseDTO {
    const response: any = {};

    performanceData.forEach((data) => {
      if (!data._id) return;

      const emailType = data._id;
      const responseKey = this.getResponseKey(emailType);

      if (!responseKey) return;

      const sent = data.sent || 0;
      const opened = data.opened || 0;
      const clicked = data.clicked || 0;

      const metrics: EmailPerformanceMetricsDTO = {
        sent,
        opened,
        openRate: sent > 0 ? Math.round((opened / sent) * 1000) / 10 : 0,
        clicked,
        clickRate: sent > 0 ? Math.round((clicked / sent) * 1000) / 10 : 0,
      };

      if (emailType === EMAIL_TYPE.END) {
        metrics.converted = converted;
        metrics.conversionRate =
          sent > 0 ? Math.round((converted / sent) * 1000) / 10 : 0;
      }

      response[responseKey] = metrics;
    });

    if (!response.start) {
      response.start = {
        sent: 0,
        opened: 0,
        openRate: 0,
        clicked: 0,
        clickRate: 0,
      };
    }

    if (!response.end) {
      response.end = {
        sent: 0,
        opened: 0,
        openRate: 0,
        clicked: 0,
        clickRate: 0,
        converted: 0,
        conversionRate: 0,
      };
    }

    return response as EmailPerformanceResponseDTO;
  }

  getResponseKey = (emailType: string): string | null => {
    if (emailType === EMAIL_TYPE.START) return 'start';
    if (emailType === EMAIL_TYPE.END) return 'end';

    // Xử lý dynamic cho other emails: other_1 -> other[0], other_2 -> other[1], etc.
    if (emailType.startsWith('other_')) {
      const indexMatch = emailType.match(/other_(\d+)/);
      if (indexMatch) {
        const index = parseInt(indexMatch[1]) - 1; // other_1 -> index 0
        return `other[${index}]`;
      }
    }

    return null; // Unknown email type
  };

  async getUserSegmentAnalysis(
    campaignId: string,
  ): Promise<UserSegmentAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const segmentData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      {
        $addFields: {
          position: {
            $ifNull: ['$metadata.job', 'Other'],
          },
        },
      },
      {
        $group: {
          _id: '$position',
          totalUsers: { $sum: 1 },
          users: { $push: '$$ROOT' },
        },
      },
      {
        $unwind: {
          path: '$users',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$users.emailTracking',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: {
            position: '$_id',
            userId: '$users.userId',
          },
          totalUsers: { $first: '$totalUsers' },
          position: { $first: '$_id' },
          emailsSent: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$users.emailTracking.status',
                    [
                      EMAIL_STATUS.SENT,
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                1,
                0,
              ],
            },
          },
          emailsOpened: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$users.emailTracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
                1,
                0,
              ],
            },
          },
          emailsClicked: {
            $sum: {
              $cond: [
                { $eq: ['$users.emailTracking.status', EMAIL_STATUS.CLICKED] },
                1,
                0,
              ],
            },
          },
          isConverted: {
            $max: {
              $cond: [
                { $eq: ['$users.status', CAMP_USER_STATUS.COMPLETED] },
                1,
                0,
              ],
            },
          },
        },
      },
      {
        $group: {
          _id: '$position',
          totalUsers: { $first: '$totalUsers' },
          totalEmailsSent: { $sum: '$emailsSent' },
          totalEmailsOpened: { $sum: '$emailsOpened' },
          totalEmailsClicked: { $sum: '$emailsClicked' },
          totalConversions: { $sum: '$isConverted' },
        },
      },
      {
        $project: {
          _id: 0,
          position: '$_id',
          totalUsers: 1,
          openRate: {
            $cond: [
              { $gt: ['$totalEmailsSent', 0] },
              {
                $multiply: [
                  { $divide: ['$totalEmailsOpened', '$totalEmailsSent'] },
                  100,
                ],
              },
              0,
            ],
          },
          clickRate: {
            $cond: [
              { $gt: ['$totalEmailsSent', 0] },
              {
                $multiply: [
                  { $divide: ['$totalEmailsClicked', '$totalEmailsSent'] },
                  100,
                ],
              },
              0,
            ],
          },
          conversionRate: {
            $cond: [
              { $gt: ['$totalUsers', 0] },
              {
                $multiply: [
                  { $divide: ['$totalConversions', '$totalUsers'] },
                  100,
                ],
              },
              0,
            ],
          },
        },
      },
      {
        $sort: { totalUsers: -1 },
      },
    ]);

    const byPosition = segmentData.map((segment) => ({
      position: segment.position || 'Other',
      totalUsers: segment.totalUsers || 0,
      openRate: Math.round((segment.openRate || 0) * 10) / 10,
      clickRate: Math.round((segment.clickRate || 0) * 10) / 10,
      conversionRate: Math.round((segment.conversionRate || 0) * 10) / 10,
    }));

    return plainToInstance(
      UserSegmentAnalysisResponseDTO,
      {
        byPosition,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  async syncUsersFromFile(
    campaignId: string,
    file: Express.Multer.File,
    authUser: User,
  ): Promise<{ success: boolean; usersCount: number }> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const csvData = await this.parseCSVFile(file);

    await this.validateCSVFormat(csvData, camp);

    await this.campUserModel.deleteMany({ campId: campaignId });

    const campUsers = csvData.map((row, index) => {
      const fullName = row.FULLNAME?.trim() || '';
      const [firstName, ...lastNameParts] = fullName.split(' ');
      const lastName = lastNameParts.join(' ') || '';

      // Build metadata with all CSV columns for template variable replacement
      const metadata: Record<string, any> = {
        originalIndex: index + 1,
        // Store original CSV data for template variables
        FULLNAME: fullName,
        fullname: fullName,
        NAME: fullName,
        name: fullName,
      };

      // Add all CSV columns to metadata for flexible template variable mapping
      Object.keys(row).forEach((key) => {
        const value = row[key]?.trim() || '';
        metadata[key] = value;
        metadata[key.toLowerCase()] = value;
        metadata[key.toUpperCase()] = value;
      });

      return {
        userId: `${campaignId}_user_${index + 1}`,
        campId: campaignId,
        email: row.EMAIL.trim().toLowerCase(),
        firstName: firstName || '',
        lastName: lastName,
        status: CAMP_USER_STATUS.ACTIVE,
        enrolledAt: new Date(),
        emailTracking: this.createInitialEmailTracking(),
        metadata,
        createdAt: new Date(),
        createdBy: authUser.telegramId,
      };
    });

    if (campUsers.length > 0) {
      await this.campUserModel.insertMany(campUsers);
    }

    return {
      success: true,
      usersCount: campUsers.length,
    };
  }

  private async parseCSVFile(file: Express.Multer.File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const stream = Readable.from(file.buffer);

      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  private async validateCSVFormat(csvData: any[], camp: any): Promise<void> {
    if (!csvData || csvData.length === 0) {
      throw new BadRequestException('CSV file is empty or invalid');
    }

    // Get required headers from email template requirements
    const emailTemplate = await this.emailTemplateModel
      .findById(camp.emailTemplate)
      .lean();

    if (!emailTemplate) {
      throw new BadRequestException('Email template not found');
    }

    const requiredHeaders = emailTemplate.requirements || [
      'EMAIL',
      'COUNTRY',
      'FULLNAME',
      'JOB',
    ];
    const firstRow = csvData[0];

    for (const header of requiredHeaders) {
      if (!(header in firstRow)) {
        throw new BadRequestException(
          `Missing required header: ${header}. Expected headers: ${requiredHeaders.join(', ')}`,
        );
      }
    }

    csvData.forEach((row, index) => {
      if (!row.EMAIL || !row.EMAIL.includes('@')) {
        throw new BadRequestException(
          `Invalid email at row ${index + 1}: ${row.EMAIL}`,
        );
      }
      if (!row.FULLNAME || row.FULLNAME.trim().length === 0) {
        throw new BadRequestException(`Missing fullname at row ${index + 1}`);
      }
    });
  }

  async getCampUsers(campId: string): Promise<CampUserResponseDTO[]> {
    const users = await this.campUserModel
      .find({ campId, deletedAt: { $exists: false } })
      .lean()
      .exec();

    return users.map((user) =>
      plainToInstance(CampUserResponseDTO, user, {
        excludeExtraneousValues: true,
      }),
    );
  }

  async getTimeAnalysis(campaignId: string): Promise<TimeAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const avgTimeToOpen = await this.calculateAvgTimeToOpen(campaignId);
    const avgTimeToConvert = await this.calculateAvgTimeToConvert(campaignId);
    const openTrend = await this.calculateOpenTrend(campaignId);
    const conversionTrend = await this.calculateConversionTrend(campaignId);

    return plainToInstance(
      TimeAnalysisResponseDTO,
      {
        avgTimeToOpen,
        avgTimeToConvert,
        openTrend,
        conversionTrend,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  private async calculateAvgTimeToOpen(campaignId: string): Promise<any> {
    const timeData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.sentAt': { $exists: true },
          'emailTracking.openedAt': { $exists: true },
          'emailTracking.status': {
            $in: [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
          },
        },
      },
      {
        $addFields: {
          timeToOpen: {
            $subtract: ['$emailTracking.openedAt', '$emailTracking.sentAt'],
          },
        },
      },
      {
        $group: {
          _id: '$emailTracking.emailType',
          avgTimeToOpenMs: { $avg: '$timeToOpen' },
        },
      },
    ]);

    const result: any = {};

    timeData.forEach((data) => {
      if (!data._id || !data.avgTimeToOpenMs) return;

      const emailType = data._id;
      const responseKey = this.getResponseKey(emailType);

      if (!responseKey) return;

      const avgTimeMs = data.avgTimeToOpenMs;
      result[responseKey] = this.formatDuration(avgTimeMs);
    });

    // Đảm bảo có ít nhất start và end
    if (!result.start) {
      result.start = '0 minutes';
    }
    if (!result.end) {
      result.end = '0 minutes';
    }

    return result;
  }

  private async calculateAvgTimeToConvert(campaignId: string): Promise<string> {
    const conversionData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
        },
      },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: '$userId',
          startTime: {
            $min: {
              $cond: [
                { $eq: ['$emailTracking.emailType', EMAIL_TYPE.START] },
                '$emailTracking.sentAt',
                null,
              ],
            },
          },
          completedTime: { $first: '$completedAt' },
        },
      },
      {
        $match: {
          startTime: { $exists: true, $ne: null },
          completedTime: { $exists: true, $ne: null },
        },
      },
      {
        $addFields: {
          timeToConvert: {
            $subtract: ['$completedTime', '$startTime'],
          },
        },
      },
      {
        $group: {
          _id: null,
          avgTimeToConvertMs: { $avg: '$timeToConvert' },
        },
      },
    ]);

    const avgTimeMs = conversionData[0]?.avgTimeToConvertMs || 0;
    return this.formatDuration(avgTimeMs);
  }

  private async calculateOpenTrend(
    campaignId: string,
  ): Promise<TrendDataDTO[]> {
    const trendData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.openedAt': { $exists: true },
          'emailTracking.status': {
            $in: [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
          },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$emailTracking.openedAt',
            },
          },
          opens: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          opens: 1,
        },
      },
    ]);

    return trendData.map((item) =>
      plainToInstance(TrendDataDTO, item, {
        excludeExtraneousValues: true,
      }),
    );
  }

  private async calculateConversionTrend(
    campaignId: string,
  ): Promise<TrendDataDTO[]> {
    const trendData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
          completedAt: { $exists: true },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$completedAt',
            },
          },
          conversions: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          conversions: 1,
        },
      },
    ]);

    return trendData.map((item) =>
      plainToInstance(TrendDataDTO, item, {
        excludeExtraneousValues: true,
      }),
    );
  }

  private formatDuration(milliseconds: number): string {
    if (milliseconds <= 0) return '0 minutes';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      const remainingHours = hours % 24;
      if (remainingHours > 0) {
        return `${days} day${days > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
      }
      return `${days} day${days > 1 ? 's' : ''}`;
    }

    if (hours > 0) {
      const remainingMinutes = minutes % 60;
      if (remainingMinutes > 0) {
        return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
      }
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }

    return `${seconds} second${seconds > 1 ? 's' : ''}`;
  }

  async getErrorAnalysis(
    campaignId: string,
  ): Promise<ErrorAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const totalEmailsSent = await this.getTotalEmailsSent(campaignId);
    const failureData = await this.getFailureAnalysis(campaignId);
    const bounceRate = await this.calculateBounceRate(
      campaignId,
      totalEmailsSent,
    );
    const nonInteractiveUsers = await this.getNonInteractiveUsers(campaignId);

    return plainToInstance(
      ErrorAnalysisResponseDTO,
      {
        failedEmails: failureData.total,
        failureReasons: failureData.reasons,
        bounceRate,
        nonInteractiveUsers,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  private async getTotalEmailsSent(campaignId: string): Promise<number> {
    const result = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': {
            $in: [
              EMAIL_STATUS.SENT,
              EMAIL_STATUS.DELIVERED,
              EMAIL_STATUS.OPENED,
              EMAIL_STATUS.CLICKED,
              EMAIL_STATUS.BOUNCED,
              EMAIL_STATUS.FAILED,
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          totalSent: { $sum: 1 },
        },
      },
    ]);

    return result[0]?.totalSent || 0;
  }

  private async getFailureAnalysis(
    campaignId: string,
  ): Promise<{ total: number; reasons: any }> {
    const failureData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': {
            $in: [EMAIL_STATUS.BOUNCED, EMAIL_STATUS.FAILED],
          },
        },
      },
      {
        $addFields: {
          failureCategory: {
            $switch: {
              branches: [
                {
                  case: {
                    $eq: ['$emailTracking.status', EMAIL_STATUS.BOUNCED],
                  },
                  then: 'bounced',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /invalid|email|address/i,
                        },
                      },
                    ],
                  },
                  then: 'invalidEmail',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /server|timeout|connection/i,
                        },
                      },
                    ],
                  },
                  then: 'serverError',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /spam|blocked/i,
                        },
                      },
                    ],
                  },
                  then: 'spamBlocked',
                },
              ],
              default: 'other',
            },
          },
        },
      },
      {
        $group: {
          _id: '$failureCategory',
          count: { $sum: 1 },
        },
      },
    ]);

    const reasons = {
      invalidEmail: 0,
      serverError: 0,
      other: 0,
      bounced: 0,
      spamBlocked: 0,
    };

    let total = 0;

    failureData.forEach((item) => {
      const category = item._id;
      const count = item.count;
      total += count;

      if (category in reasons) {
        reasons[category] = count;
      } else {
        reasons.other += count;
      }
    });

    return { total, reasons };
  }

  private async calculateBounceRate(
    campaignId: string,
    totalEmailsSent: number,
  ): Promise<number> {
    if (totalEmailsSent === 0) return 0;

    const bouncedCount = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': EMAIL_STATUS.BOUNCED,
        },
      },
      {
        $group: {
          _id: null,
          bounced: { $sum: 1 },
        },
      },
    ]);

    const bounced = bouncedCount[0]?.bounced || 0;
    return Math.round((bounced / totalEmailsSent) * 1000) / 10;
  }

  private async getNonInteractiveUsers(campaignId: string): Promise<number> {
    const result = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      {
        $addFields: {
          hasInteraction: {
            $anyElementTrue: {
              $map: {
                input: '$emailTracking',
                as: 'tracking',
                in: {
                  $in: [
                    '$$tracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
              },
            },
          },
        },
      },
      {
        $match: {
          hasInteraction: { $ne: true },
        },
      },
      {
        $group: {
          _id: null,
          nonInteractiveCount: { $sum: 1 },
        },
      },
    ]);

    return result[0]?.nonInteractiveCount || 0;
  }

  // Helper method để tạo initial email tracking cho user
  private createInitialEmailTracking(): any[] {
    return [
      {
        emailType: EMAIL_TYPE.START,
        status: EMAIL_STATUS.PENDING,
        openCount: 0,
        clickCount: 0,
      },
    ];
  }

  /**
   * Start email campaign execution for a camp (4-stage flow)
   */
  async startCampaign(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsStarted: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      if (!(camp as any).emailTemplate) {
        throw new BadRequestException(
          'No email campaign associated with this camp',
        );
      }

      // Check if campaign status allows starting
      const currentStatus = (camp as any).status;
      this.validateCampaignStatusForStart(currentStatus);

      // Get all camp users
      const campUsers = await this.campUserModel.find({
        campId: campId,
        status: CAMP_USER_STATUS.ACTIVE,
      });

      if (campUsers.length === 0) {
        throw new BadRequestException('No active users found in this camp');
      }

      let executionsStarted = 0;

      // Start 4-stage campaign for each user
      for (const user of campUsers) {
        try {
          await this.campaignStageFlowService.startCampaignForUser(
            campId,
            user.userId,
            user.email,
          );
          executionsStarted++;
        } catch (error) {
          this.logger.error(
            `Failed to start campaign for user ${user.userId}:`,
            error,
          );
          // Continue with other users
        }
      }

      // Update camp status to ACTIVE
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.ACTIVE,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return {
        success: true,
        message: `4-stage campaign started successfully for ${executionsStarted} users`,
        executionsStarted,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to start campaign: ${error.message}`,
      );
    }
  }

  /**
   * Stop email campaign execution for a camp
   */
  async stopCampaign(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsStopped: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Check if campaign status allows stopping
      const currentStatus = (camp as any).status;
      this.validateCampaignStatusForStop(currentStatus);

      // Stop campaign execution
      const result = await this.campaignExecutionService.stopCampaign(campId);

      // Update camp status to CANCELLED
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.CANCELLED,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to stop campaign: ${error.message}`,
      );
    }
  }

  /**
   * Get campaign execution status
   */
  async getCampaignExecutionStatus(
    campId: string,
    _authUser: User,
  ): Promise<{
    totalExecutions: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    paused: number;
    cancelled: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Get execution status
      const status =
        await this.campaignExecutionService.getCampaignExecutionStatus(campId);

      return status;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get campaign status: ${error.message}`,
      );
    }
  }

  /**
   * Pause email campaign execution for a camp
   */
  async pauseCampaign(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsPaused: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      if (!(camp as any).emailTemplate) {
        throw new BadRequestException(
          'No email campaign associated with this camp',
        );
      }

      // Check if campaign status allows pausing
      const currentStatus = (camp as any).status;
      this.validateCampaignStatusForPause(currentStatus);

      // Pause all campaign progresses for this camp
      const result =
        await this.campaignStageWebhookService.pauseAllCampaigns(campId);

      // Update camp status to PAUSED
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.PAUSED,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to pause campaign: ${error.message}`,
      );
    }
  }

  /**
   * Resume email campaign execution for a camp
   */
  async resumeCampaign(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsResumed: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      if (!(camp as any).emailTemplate) {
        throw new BadRequestException(
          'No email campaign associated with this camp',
        );
      }

      // Check if campaign status allows resuming
      const currentStatus = (camp as any).status;
      this.validateCampaignStatusForResume(currentStatus);

      // Resume all campaign progresses for this camp
      const result =
        await this.campaignStageWebhookService.resumeAllCampaigns(campId);

      // Update camp status to ACTIVE
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.ACTIVE,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to resume campaign: ${error.message}`,
      );
    }
  }

  /**
   * Cancel email campaign execution for a camp
   */
  async cancelCampaign(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    executionsCancelled: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      if (!(camp as any).emailTemplate) {
        throw new BadRequestException(
          'No email campaign associated with this camp',
        );
      }

      // Cancel all campaign progresses for this camp
      const result =
        await this.campaignStageWebhookService.cancelAllCampaigns(campId);

      // Update camp status to CANCELLED
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.CANCELLED,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to cancel campaign: ${error.message}`,
      );
    }
  }

  /**
   * Get campaign stage progress for all users in a camp
   */
  async getCampaignProgress(campId: string, _authUser: User): Promise<any> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Get campaign progresses
      const progresses =
        await this.campaignStageWebhookService.getCampaignProgresses(campId);

      return {
        success: true,
        campId,
        totalUsers: progresses.length,
        progresses: progresses.map((progress) => ({
          userId: progress.userId,
          email: progress.email,
          overallStatus: progress.overallStatus,
          currentStage: progress.currentStage,
          currentEmailIndex: progress.currentEmailIndex,
          stageStatuses: {
            initiation: progress.initiationStatus,
            engagement: progress.engagementStatus,
            conversion: progress.conversionStatus,
            retention: progress.retentionStatus,
          },
          timestamps: {
            initiationStarted: progress.initiationStartedAt,
            initiationCompleted: progress.initiationCompletedAt,
            engagementStarted: progress.engagementStartedAt,
            engagementCompleted: progress.engagementCompletedAt,
            conversionStarted: progress.conversionStartedAt,
            conversionCompleted: progress.conversionCompletedAt,
            retentionStarted: progress.retentionStartedAt,
            retentionCompleted: progress.retentionCompletedAt,
          },
          emailTracking: {
            sentEmails: progress.sentEmailIds.length,
            openedEmails: progress.openedEmailIds.length,
            clickedEmails: progress.clickedEmailIds.length,
          },
          nextExecutionTime: progress.nextExecutionTime,
        })),
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get campaign progress: ${error.message}`,
      );
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campId: string, _authUser: User): Promise<any> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Get campaign statistics
      const stats =
        await this.campaignStageWebhookService.getCampaignStats(campId);

      return {
        success: true,
        campId,
        ...stats,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get campaign stats: ${error.message}`,
      );
    }
  }

  /**
   * Pause campaign for a specific user
   */
  async pauseUserCampaign(
    campId: string,
    email: string,
    _authUser: User,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Pause campaign for user
      const success = await this.campaignStageWebhookService.pauseCampaign(
        campId,
        email,
      );

      if (!success) {
        throw new BadRequestException(
          'User not found or campaign already paused',
        );
      }

      return {
        success: true,
        message: `Campaign paused successfully for user ${email}`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to pause user campaign: ${error.message}`,
      );
    }
  }

  /**
   * Resume campaign for a specific user
   */
  async resumeUserCampaign(
    campId: string,
    email: string,
    _authUser: User,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Resume campaign for user
      const success = await this.campaignStageWebhookService.resumeCampaign(
        campId,
        email,
      );

      if (!success) {
        throw new BadRequestException('User not found or campaign not paused');
      }

      return {
        success: true,
        message: `Campaign resumed successfully for user ${email}`,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to resume user campaign: ${error.message}`,
      );
    }
  }

  /**
   * Reset user to Conversion stage for testing
   */
  async resetUserToConversionStage(
    campId: string,
    email: string,
    _authUser: User,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Reset user to conversion stage
      const result =
        await this.campaignStageWebhookService.resetUserToConversionStage(
          campId,
          email,
        );

      return result;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to reset user to conversion stage: ${error.message}`,
      );
    }
  }

  /**
   * Clear all running campaign processes
   */
  async clearAllProcesses(authUser: User): Promise<{
    success: boolean;
    message: string;
    clearedExecutions: number;
    clearedProgresses: number;
  }> {
    try {
      // Clear all campaign stage progresses
      const progressResult =
        await this.campaignStageWebhookService.clearAllProgresses();

      // Clear all campaign executions (if using old execution system)
      const executionResult =
        await this.campaignExecutionService.clearAllExecutions();

      // Set all active camps to paused
      const campUpdateResult = await this.campModel.updateMany(
        { status: CAMP_STATUS.ACTIVE },
        {
          status: CAMP_STATUS.PAUSED,
          updatedAt: new Date(),
          updatedBy: authUser.telegramId,
        },
      );

      return {
        success: true,
        message: `All processes cleared successfully. ${progressResult.cleared} progresses and ${executionResult.cleared} executions cleared, ${campUpdateResult.modifiedCount} camps paused.`,
        clearedExecutions: executionResult.cleared,
        clearedProgresses: progressResult.cleared,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to clear all processes: ${error.message}`,
      );
    }
  }

  /**
   * Clear all processes for a specific camp
   */
  async clearCampProcesses(
    campId: string,
    authUser: User,
  ): Promise<{
    success: boolean;
    message: string;
    clearedExecutions: number;
    clearedProgresses: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel
        .findOneWithAuth({ _id: campId }, authUser)
        .lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Clear campaign stage progresses for this camp
      const progressResult =
        await this.campaignStageWebhookService.clearCampProgresses(campId);

      // Clear campaign executions for this camp (if using old execution system)
      const executionResult =
        await this.campaignExecutionService.clearCampExecutions(campId);

      // Set camp to paused
      await this.campModel.findByIdAndUpdate(campId, {
        status: CAMP_STATUS.PAUSED,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      });

      return {
        success: true,
        message: `Camp processes cleared successfully. ${progressResult.cleared} progresses and ${executionResult.cleared} executions cleared.`,
        clearedExecutions: executionResult.cleared,
        clearedProgresses: progressResult.cleared,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to clear camp processes: ${error.message}`,
      );
    }
  }

  /**
   * Get webhook logs for a specific camp
   */
  async getCampWebhookLogs(
    campId: string,
    filters: {
      status?: string;
      limit: number;
      skip: number;
      email?: string;
      startDate?: string;
      endDate?: string;
      processingStatus?: string;
    },
  ): Promise<{
    success: boolean;
    data: any[];
    total: number;
    skip: number;
    limit: number;
  }> {
    try {
      // Verify camp exists and user has access
      const camp = await this.campModel.findOne({ _id: campId }).lean();

      if (!camp) {
        throw new BadRequestException('Camp not found or access denied');
      }

      // Build query for Email webhook logs
      const query: any = { campId };

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.email) {
        query.email = { $regex: filters.email, $options: 'i' };
      }

      if (filters.startDate || filters.endDate) {
        query.timestamp = {};
        if (filters.startDate) {
          query.timestamp.$gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          query.timestamp.$lte = new Date(filters.endDate);
        }
      }

      // Get Email webhook logs
      const [logs, total] = await Promise.all([
        this.emailWebhookLogModel
          .find(query)
          .sort({ timestamp: -1 })
          .skip(filters.skip)
          .limit(filters.limit)
          .lean(),
        this.emailWebhookLogModel.countDocuments(query),
      ]);

      return {
        success: true,
        data: logs.map((log) => ({
          _id: log._id,
          campId: log.campId,
          email: log.email,
          messageId: log.messageId,
          status: log.status,
          emailType: log.emailType,
          timestamp: log.timestamp,
          webhookData: {
            subject: log.subject,
            emailType: log.emailType,
            status: log.status,
            errorMessage: log.errorMessage,
            bounceReason: log.bounceReason,
            clickedUrl: log.clickedUrl,
            ipAddress: log.ipAddress,
            userAgent: log.userAgent,
            rawPayload: log.rawPayload,
          },
          createdAt: log.createdAt,
        })),
        total,
        skip: filters.skip,
        limit: filters.limit,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get webhook logs: ${error.message}`,
      );
    }
  }

  /**
   * Get email monitoring data for UI dashboard
   */
  async getEmailMonitoring(
    campId: string,
    filters: {
      status: string;
      search?: string;
      limit: number;
      skip: number;
    },
  ): Promise<{
    success: boolean;
    data: any[];
    total: number;
    skip: number;
    limit: number;
    statistics: any;
  }> {
    try {
      // Verify camp exists
      const camp = await this.campModel.findOne({ _id: campId }).lean();
      if (!camp) {
        throw new BadRequestException('Camp not found');
      }

      // Build query for Email webhook logs
      const query: any = { campId };

      // Status filter
      if (filters.status && filters.status !== 'all') {
        query.status = filters.status;
      }

      // Search filter (email, name, or subject)
      if (filters.search) {
        query.$or = [
          { email: { $regex: filters.search, $options: 'i' } },
          { subject: { $regex: filters.search, $options: 'i' } },
        ];
      }

      // Get unique emails with their latest status using aggregation
      const pipeline: any[] = [
        { $match: query },
        {
          $sort: { timestamp: -1 }, // Sort by timestamp descending to get latest first
        },
        {
          $group: {
            _id: '$email', // Group by email to get unique emails
            latestLog: { $first: '$$ROOT' }, // Get the latest log for each email
            allStatuses: { $push: '$status' }, // Collect all statuses for priority logic
            allLogs: { $push: '$$ROOT' }, // Keep all logs for extracting specific timestamps
          },
        },
        {
          $addFields: {
            // Determine the highest priority status for this email
            finalStatus: {
              $switch: {
                branches: [
                  {
                    case: { $in: ['clicked', '$allStatuses'] },
                    then: 'clicked',
                  },
                  { case: { $in: ['opened', '$allStatuses'] }, then: 'opened' },
                  {
                    case: { $in: ['delivered', '$allStatuses'] },
                    then: 'delivered',
                  },
                  { case: { $in: ['sent', '$allStatuses'] }, then: 'sent' },
                  {
                    case: { $in: ['bounced', '$allStatuses'] },
                    then: 'bounced',
                  },
                  { case: { $in: ['failed', '$allStatuses'] }, then: 'failed' },
                ],
                default: 'pending',
              },
            },
            // Extract openedAt timestamp
            openedAt: {
              $let: {
                vars: {
                  openedLog: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: '$allLogs',
                          cond: { $eq: ['$$this.status', 'opened'] },
                        },
                      },
                      0,
                    ],
                  },
                },
                in: '$$openedLog.timestamp',
              },
            },
            // Extract clickedAt timestamp
            clickedAt: {
              $let: {
                vars: {
                  clickedLog: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: '$allLogs',
                          cond: { $eq: ['$$this.status', 'clicked'] },
                        },
                      },
                      0,
                    ],
                  },
                },
                in: '$$clickedLog.timestamp',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                '$latestLog',
                {
                  status: '$finalStatus',
                  openedAt: '$openedAt',
                  clickedAt: '$clickedAt',
                },
              ],
            },
          },
        },
        { $sort: { timestamp: -1 } }, // Sort final results by timestamp
        { $skip: Number(filters.skip) },
        { $limit: Number(filters.limit) },
      ];

      // Get total count of unique emails
      const countPipeline = [
        { $match: query },
        { $group: { _id: '$email' } },
        { $count: 'total' },
      ];

      const [logs, totalResult] = await Promise.all([
        this.emailWebhookLogModel.aggregate(pipeline),
        this.emailWebhookLogModel.aggregate(countPipeline),
      ]);

      const total = totalResult[0]?.total || 0;

      // Get camp users for additional info
      const campUsers = await this.campUserModel.find({ campId }).lean();

      // Create user lookup map
      const userLookup = new Map();
      campUsers.forEach((user) => {
        userLookup.set(user.email, user);
      });

      // Transform data for UI
      const data = logs.map((log) => {
        const user = userLookup.get(log.email);

        return {
          id: log._id,
          recipient: {
            name:
              this.getUserFullName(user) ||
              this.extractNameFromEmail(log.email),
            email: log.email,
          },
          subject: log.subject || 'Welcome to our service!',
          status: log.status, // This is now the finalStatus from aggregation
          sentAt: this.formatDateTime(log.timestamp || new Date()),
          actions: {
            openedAt: log.openedAt ? this.formatDateTime(log.openedAt) : null,
            clickedAt: log.clickedAt
              ? this.formatDateTime(log.clickedAt)
              : null,
          },
          device: this.detectDevice(log.rawPayload),
          location: this.detectLocation(log.ipAddress),
          messageId: log.messageId,
          createdAt: log.createdAt,
        };
      });

      // Get statistics
      const statistics = await this.getEmailStatistics(campId);

      return {
        success: true,
        data,
        total,
        skip: filters.skip,
        limit: filters.limit,
        statistics,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get email monitoring data: ${error.message}`,
      );
    }
  }

  /**
   * Get email statistics for dashboard
   */
  private async getEmailStatistics(campId: string): Promise<any> {
    try {
      const stats = await this.emailWebhookLogModel.aggregate([
        { $match: { campId } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
          },
        },
      ]);

      const result = {
        totalEmails: 0,
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        bounced: 0,
        failed: 0,
        pending: 0,
      };

      stats.forEach((stat) => {
        const status = stat._id;
        if (result.hasOwnProperty(status)) {
          result[status] = stat.count;
        }
        result.totalEmails += stat.count;
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to get email statistics:', error);
      return {
        totalEmails: 0,
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        bounced: 0,
        failed: 0,
        pending: 0,
      };
    }
  }

  /**
   * Helper methods for data transformation
   */
  private extractNameFromEmail(email: string): string {
    const username = email.split('@')[0];
    return username.charAt(0).toUpperCase() + username.slice(1);
  }

  private getUserFullName(user: any): string | null {
    if (!user) return null;

    const firstName = user.firstName || '';
    const lastName = user.lastName || '';

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (lastName) {
      return lastName;
    }

    return null;
  }

  private mapEventTypeToStatus(eventType: string): string {
    // Normalize to lowercase for consistent mapping
    const normalizedEventType = eventType?.toLowerCase();

    const mapping: Record<string, string> = {
      // SNS format (lowercase)
      send: 'sent',
      delivery: 'delivered',
      open: 'opened',
      click: 'clicked',
      bounce: 'bounced',
      reject: 'failed',
      complaint: 'failed',
      renderingfailure: 'failed',
      deliverydelay: 'pending',

      // EventBridge format (normalized to lowercase)
      sent: 'sent',
      delivered: 'delivered',
      opened: 'opened',
      clicked: 'clicked',
      bounced: 'bounced',
      rejected: 'failed',
      complained: 'failed',
    };

    return mapping[normalizedEventType] || 'pending';
  }

  private formatDateTime(date: Date): string {
    if (!date) return '';
    const d = new Date(date);
    const month = d.toLocaleDateString('en-US', { month: 'short' });
    const day = d.getDate();
    const time = d.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
    return `${month} ${day}, ${time}`;
  }

  private detectDevice(rawPayload: any): string {
    if (!rawPayload) return 'Unknown';

    // Try to extract user agent from raw payload
    const userAgent =
      rawPayload.userAgent || rawPayload.detail?.userAgent || '';

    if (userAgent.includes('iPhone') || userAgent.includes('iOS')) return 'iOS';
    if (userAgent.includes('iPad')) return 'iPadOS';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Macintosh') || userAgent.includes('Mac OS'))
      return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';

    // Fallback to random assignment for demo
    const devices = ['iOS', 'Android', 'Windows', 'macOS', 'iPadOS'];
    return devices[Math.floor(Math.random() * devices.length)];
  }

  private detectLocation(ipAddress?: string): string {
    if (!ipAddress) {
      return 'Unknown Location';
    }

    try {
      // Use geoip-lite to detect location from IP address
      const geo = geoip.lookup(ipAddress);

      if (geo) {
        // Format: City, Country or Country if no city
        if (geo.city) {
          return `${geo.city}, ${geo.country}`;
        } else {
          return geo.country;
        }
      }

      // If geoip lookup fails, return IP with unknown location
      return `Unknown (${ipAddress})`;
    } catch (error) {
      this.logger.error(
        `Failed to detect location for IP ${ipAddress}:`,
        error,
      );
      return `Unknown (${ipAddress})`;
    }
  }

  /**
   * Export email monitoring data as CSV
   */
  async exportEmailMonitoringCSV(
    campId: string,
    filters: {
      status: string;
      search?: string;
    },
  ): Promise<string> {
    try {
      // Get all data without pagination for export
      const emailData = await this.getEmailMonitoring(campId, {
        ...filters,
        limit: 10000, // Large limit for export
        skip: 0,
      });

      // Get camp and email template for dynamic headers
      const camp = await this.campModel.findById(campId).lean();
      if (!camp) {
        throw new BadRequestException('Camp not found');
      }

      const emailTemplate = await this.emailTemplateModel
        .findById(camp.emailTemplate)
        .lean();

      const requirements = emailTemplate?.requirements || [
        'EMAIL',
        'COUNTRY',
        'FULLNAME',
        'JOB',
      ];

      // Build CSV headers based on requirements + email monitoring fields
      const headers = [
        'Recipient Name',
        'Recipient Email',
        'Subject',
        'Status',
        'Sent At',
        'Opened At',
        'Clicked At',
        'Device',
        'Location',
        'Message ID',
        ...requirements.filter((req) => !['EMAIL', 'FULLNAME'].includes(req)), // Add other requirements except EMAIL and FULLNAME (already covered)
      ];

      // Get camp users for additional metadata
      const campUsers = await this.campUserModel.find({ campId }).lean();
      const userLookup = new Map();
      campUsers.forEach((user) => {
        userLookup.set(user.email, user);
      });

      // Convert data to CSV rows
      const rows = emailData.data.map((item) => {
        const user = userLookup.get(item.recipient.email);
        const baseRow = [
          item.recipient.name,
          item.recipient.email,
          item.subject,
          item.status,
          item.sentAt,
          item.actions.openedAt || '',
          item.actions.clickedAt || '',
          item.device,
          item.location,
          item.messageId,
        ];

        // Add additional fields based on requirements
        const additionalFields = requirements
          .filter((req) => !['EMAIL', 'FULLNAME'].includes(req))
          .map((req) => {
            if (req === 'COUNTRY') {
              return user?.metadata?.country || '';
            } else if (req === 'JOB') {
              return user?.metadata?.job || '';
            } else {
              // For other custom requirements, try to get from metadata
              return user?.metadata?.[req.toLowerCase()] || '';
            }
          });

        return [...baseRow, ...additionalFields];
      });

      // Combine headers and rows
      const csvContent = [headers, ...rows]
        .map((row) => row.map((field) => `"${field}"`).join(','))
        .join('\n');

      return csvContent;
    } catch (error) {
      throw new BadRequestException(`Failed to export CSV: ${error.message}`);
    }
  }

  /**
   * Get detailed email information
   */
  async getEmailDetails(
    campId: string,
    emailId: string,
  ): Promise<{
    success: boolean;
    data: any;
  }> {
    try {
      // Get the specific webhook log
      const log = await this.emailWebhookLogModel
        .findOne({ _id: emailId, campId })
        .lean();

      if (!log) {
        throw new BadRequestException('Email log not found');
      }

      // Get all related logs for this email/messageId
      const relatedLogs = await this.emailWebhookLogModel
        .find({
          campId,
          $or: [{ messageId: log.messageId }, { email: log.email }],
        })
        .sort({ timestamp: 1 })
        .lean();

      // Get user info
      const user = await this.campUserModel
        .findOne({ campId, email: log.email })
        .lean();

      // Build timeline from related logs
      const timeline = relatedLogs.map((relatedLog) => ({
        event: relatedLog.status,
        timestamp: this.formatDateTime(relatedLog.timestamp || new Date()),
        details: {
          emailType: relatedLog.emailType,
          messageId: relatedLog.messageId,
          status: relatedLog.status,
        },
      }));

      // Get email template for content
      const camp = await this.campModel
        .findById(campId)
        .populate('emailTemplate')
        .lean();

      const emailTemplate = camp?.emailTemplate as any;

      const data = {
        id: log._id,
        recipient: {
          name:
            this.getUserFullName(user) || this.extractNameFromEmail(log.email),
          email: log.email,
        },
        subject: log.subject || 'Welcome to our service!',
        content: this.getEmailContent(emailTemplate, log.emailType),
        status: log.status,
        timeline,
        device: this.detectDevice(log.rawPayload),
        location: this.detectLocation(log.ipAddress),
        ipAddress: log.ipAddress || 'Unknown',
        userAgent: log.rawPayload?.userAgent || 'Unknown',
        webhookLogs: relatedLogs.map((relatedLog) => ({
          emailType: relatedLog.emailType,
          status: relatedLog.status,
          timestamp: this.formatDateTime(relatedLog.timestamp || new Date()),
          rawData: relatedLog.rawPayload,
        })),
      };

      return {
        success: true,
        data,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get email details: ${error.message}`,
      );
    }
  }

  /**
   * Get email content based on template and event type
   */
  private getEmailContent(emailTemplate: any, _eventType: string): string {
    if (!emailTemplate) {
      return 'Welcome to our service! We are excited to have you on board.';
    }

    // Try to get content from different flows based on event type
    const flows = [
      ...(emailTemplate.startFlow || []),
      ...(emailTemplate.remindFlow || []),
      ...(emailTemplate.endFlow || []),
    ];

    const emailFlow = flows.find((flow) => flow.type === 'email');

    if (emailFlow && emailFlow.content) {
      return emailFlow.content;
    }

    return 'Welcome to our service! We are excited to have you on board.';
  }

  /**
   * Get monitoring overview - API 1
   * Endpoint: GET /api/camps/{id}/monitoring-overview
   * Purpose: Campaign overview and statistics (cacheable)
   */
  async getMonitoringOverview(
    campId: string,
  ): Promise<MonitoringOverviewResponseDTO> {
    try {
      // Get campaign info
      const camp = await this.campModel
        .findById(campId)
        .populate('emailTemplate')
        .lean();

      if (!camp) {
        throw new BadRequestException('CAMPAIGN_NOT_FOUND');
      }

      const emailTemplate = camp.emailTemplate as any;

      // Get all webhook logs for statistics
      const allLogs = await this.emailWebhookLogModel.find({ campId }).lean();

      // Calculate statistics
      const statistics = this.calculateCampaignStatistics(allLogs);

      // Build campaign info
      const campaignInfo: CampaignOverviewInfoDTO = {
        id: camp._id.toString(),
        name: camp.brand || 'Campaign',
        status: camp.status || 'unknown',
        startDate: camp.startDate
          ? camp.startDate.toISOString()
          : new Date().toISOString(),
        createdAt: camp.createdAt
          ? camp.createdAt.toISOString()
          : new Date().toISOString(),
        lastUpdated: camp.updatedAt
          ? camp.updatedAt.toISOString()
          : new Date().toISOString(),
        emailSubject: this.getEmailSubjectFromTemplate(emailTemplate),
        brand: camp.brand || 'Unknown',
        createdBy: camp.createdByUsername || 'system',
      };

      return {
        success: true,
        campaign: campaignInfo,
        statistics,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`INTERNAL_ERROR: ${error.message}`);
    }
  }

  /**
   * Get monitoring emails - API 2
   * Endpoint: GET /api/camps/{id}/monitoring-emails
   * Purpose: Real-time email list (no cache)
   */
  /**
   * Export monitoring data in various formats
   */
  async exportMonitoringData(
    campId: string,
    type: 'csv' | 'xlsx' | 'pdf' | 'json',
    res: any,
  ): Promise<void> {
    // Get monitoring data
    const overview = await this.getMonitoringOverview(campId);
    const emails = await this.getMonitoringEmails(campId, {
      skip: 0,
      limit: 10000, // Get all emails for export
    });

    const camp = await this.campModel.findById(campId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const exportData = {
      campaign: {
        id: overview.campaign.id,
        name: overview.campaign.name,
        brand: overview.campaign.brand,
        status: overview.campaign.status,
        startDate: overview.campaign.startDate,
        totalEmails: overview.statistics.totalEmails,
        emailsSent: overview.statistics.sent,
        emailsOpened: overview.statistics.opened,
        emailsClicked: overview.statistics.clicked,
        openRate: overview.statistics.openRate,
        clickRate: overview.statistics.clickRate,
        exportedAt: new Date().toISOString(),
      },
      emails: emails.data.map((email: any) => ({
        recipient: email.email,
        subject: email.subject,
        status: email.status,
        sentAt: email.sentAt,
        openedAt: email.openedAt,
        clickedAt: email.clickedAt,
        device: email.device,
        location: email.location,
        messageId: email.messageId,
      })),
    };

    const filename = `campaign-${overview.campaign.name.replace(/[^a-zA-Z0-9]/g, '-')}-${new Date().toISOString().split('T')[0]}`;

    switch (type) {
      case 'json':
        await this.exportAsJson(exportData, filename, res);
        break;
      case 'csv':
        await this.exportAsCsv(exportData, filename, res);
        break;
      case 'xlsx':
        await this.exportAsXlsx(exportData, filename, res);
        break;
      case 'pdf':
        await this.exportAsPdf(exportData, filename, res);
        break;
      default:
        throw new BadRequestException(
          'Invalid export type. Supported: csv, xlsx, pdf, json',
        );
    }
  }

  /**
   * Export as JSON
   */
  private async exportAsJson(
    data: any,
    filename: string,
    res: any,
  ): Promise<void> {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${filename}.json"`,
    );
    res.send(JSON.stringify(data, null, 2));
  }

  /**
   * Export as CSV
   */
  private async exportAsCsv(
    data: any,
    filename: string,
    res: any,
  ): Promise<void> {
    const csvRows: string[] = [];

    // Campaign summary header
    csvRows.push('Campaign Summary');
    csvRows.push(`Campaign ID,${data.campaign.id}`);
    csvRows.push(`Campaign Name,${data.campaign.name}`);
    csvRows.push(`Brand,${data.campaign.brand}`);
    csvRows.push(`Status,${data.campaign.status}`);
    csvRows.push(`Start Date,${data.campaign.startDate}`);
    csvRows.push(`Total Users,${data.campaign.totalUsers}`);
    csvRows.push(`Total Emails,${data.campaign.totalEmails}`);
    csvRows.push(`Emails Sent,${data.campaign.emailsSent}`);
    csvRows.push(`Emails Opened,${data.campaign.emailsOpened}`);
    csvRows.push(`Emails Clicked,${data.campaign.emailsClicked}`);
    csvRows.push(`Open Rate,${data.campaign.openRate}%`);
    csvRows.push(`Click Rate,${data.campaign.clickRate}%`);
    csvRows.push(`Exported At,${data.campaign.exportedAt}`);
    csvRows.push(''); // Empty row

    // Email details header
    csvRows.push('Email Details');
    csvRows.push(
      'Recipient,Subject,Status,Sent At,Opened At,Clicked At,Device,Location,Message ID,Stage,Email Type',
    );

    // Email data
    data.emails.forEach((email: any) => {
      const row = [
        email.recipient || '',
        `"${(email.subject || '').replace(/"/g, '""')}"`, // Escape quotes
        email.status || '',
        email.sentAt || '',
        email.openedAt || '',
        email.clickedAt || '',
        email.device || '',
        email.location || '',
        email.messageId || '',
        email.stage || '',
        email.emailType || '',
      ].join(',');
      csvRows.push(row);
    });

    const csvContent = csvRows.join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${filename}.csv"`,
    );
    res.send(csvContent);
  }

  /**
   * Export as XLSX (Excel)
   */
  private async exportAsXlsx(
    data: any,
    filename: string,
    res: any,
  ): Promise<void> {
    try {
      const XLSX = require('xlsx');

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Campaign summary sheet
      const summaryData = [
        ['Campaign Summary', ''],
        ['Campaign ID', data.campaign.id],
        ['Campaign Name', data.campaign.name],
        ['Brand', data.campaign.brand],
        ['Status', data.campaign.status],
        ['Start Date', data.campaign.startDate],
        ['Total Users', data.campaign.totalUsers],
        ['Total Emails', data.campaign.totalEmails],
        ['Emails Sent', data.campaign.emailsSent],
        ['Emails Opened', data.campaign.emailsOpened],
        ['Emails Clicked', data.campaign.emailsClicked],
        ['Open Rate', `${data.campaign.openRate}%`],
        ['Click Rate', `${data.campaign.clickRate}%`],
        ['Exported At', data.campaign.exportedAt],
      ];

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Campaign Summary');

      // Email details sheet
      const emailHeaders = [
        'Recipient',
        'Subject',
        'Status',
        'Sent At',
        'Opened At',
        'Clicked At',
        'Device',
        'Location',
        'Message ID',
        'Stage',
        'Email Type',
      ];
      const emailData = [
        emailHeaders,
        ...data.emails.map((email: any) => [
          email.recipient || '',
          email.subject || '',
          email.status || '',
          email.sentAt || '',
          email.openedAt || '',
          email.clickedAt || '',
          email.device || '',
          email.location || '',
          email.messageId || '',
          email.stage || '',
          email.emailType || '',
        ]),
      ];

      const emailSheet = XLSX.utils.aoa_to_sheet(emailData);
      XLSX.utils.book_append_sheet(workbook, emailSheet, 'Email Details');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}.xlsx"`,
      );
      res.send(buffer);
    } catch (error) {
      this.logger.error('Failed to export XLSX:', error);
      throw new BadRequestException(
        'Failed to generate XLSX file. Please install xlsx package.',
      );
    }
  }

  /**
   * Export as PDF
   */
  private async exportAsPdf(
    data: any,
    filename: string,
    res: any,
  ): Promise<void> {
    try {
      const PDFDocument = require('pdfkit');

      const doc = new PDFDocument();

      // Set response headers
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}.pdf"`,
      );

      // Pipe PDF to response
      doc.pipe(res);

      // Title
      doc.fontSize(20).text('Campaign Monitoring Report', { align: 'center' });
      doc.moveDown();

      // Campaign Summary
      doc.fontSize(16).text('Campaign Summary', { underline: true });
      doc.moveDown(0.5);

      doc.fontSize(12);
      doc.text(`Campaign ID: ${data.campaign.id}`);
      doc.text(`Campaign Name: ${data.campaign.name}`);
      doc.text(`Brand: ${data.campaign.brand}`);
      doc.text(`Status: ${data.campaign.status}`);
      doc.text(`Start Date: ${data.campaign.startDate}`);
      doc.text(`Total Users: ${data.campaign.totalUsers}`);
      doc.text(`Total Emails: ${data.campaign.totalEmails}`);
      doc.text(`Emails Sent: ${data.campaign.emailsSent}`);
      doc.text(`Emails Opened: ${data.campaign.emailsOpened}`);
      doc.text(`Emails Clicked: ${data.campaign.emailsClicked}`);
      doc.text(`Open Rate: ${data.campaign.openRate}%`);
      doc.text(`Click Rate: ${data.campaign.clickRate}%`);
      doc.text(`Exported At: ${data.campaign.exportedAt}`);

      doc.moveDown();

      // Email Statistics
      doc.fontSize(16).text('Email Statistics Summary', { underline: true });
      doc.moveDown(0.5);

      doc.fontSize(12);
      doc.text(`Total Emails in Report: ${data.emails.length}`);

      // Count by status
      const statusCounts = data.emails.reduce((acc: any, email: any) => {
        acc[email.status] = (acc[email.status] || 0) + 1;
        return acc;
      }, {});

      Object.entries(statusCounts).forEach(([status, count]) => {
        doc.text(`${status}: ${count}`);
      });

      doc.moveDown();

      // Note about detailed data
      doc
        .fontSize(10)
        .text(
          'Note: For detailed email list, please use CSV or XLSX export format.',
          { align: 'center', italics: true },
        );

      // Finalize PDF
      doc.end();
    } catch (error) {
      this.logger.error('Failed to export PDF:', error);
      throw new BadRequestException(
        'Failed to generate PDF file. Please install pdfkit package.',
      );
    }
  }

  async getMonitoringEmails(
    campId: string,
    query: MonitoringEmailsQueryDTO,
  ): Promise<MonitoringEmailsResponseDTO> {
    try {
      // Validate campaign exists
      const camp = await this.campModel.findById(campId).lean();
      if (!camp) {
        throw new BadRequestException('CAMPAIGN_NOT_FOUND');
      }

      // Build query filters
      const filters = { campId, ...this.buildEmailFilters(query) };

      // Build sort options
      const sortOptions = this.buildSortOptions(query.sortBy, query.sortOrder);

      // Get paginated email logs
      const [logs, total] = await Promise.all([
        this.emailWebhookLogModel
          .find(filters)
          .sort(sortOptions)
          .skip(query.skip || 0)
          .limit(query.limit || 50)
          .lean(),
        this.emailWebhookLogModel.countDocuments(filters),
      ]);

      // Get camp users for additional info
      const emails = logs.map((log) => log.email);
      const campUsers = await this.campUserModel
        .find({ campId, email: { $in: emails } })
        .lean();

      const userLookup = new Map();
      campUsers.forEach((user) => {
        userLookup.set(user.email, user);
      });

      // Transform to response format
      const emailItems: MonitoringEmailItemDTO[] = logs.map((log) => {
        const user = userLookup.get(log.email);

        const normalizedStatus = this.normalizeEmailStatus(log.status);

        return {
          id: log._id.toString(),
          email: log.email,
          name:
            this.getUserFullName(user) || this.extractNameFromEmail(log.email),
          subject: log.subject || 'Welcome to our service!',
          status: normalizedStatus,
          sentAt: log.timestamp
            ? log.timestamp.toISOString()
            : new Date().toISOString(),
          openedAt:
            normalizedStatus === 'opened' || normalizedStatus === 'clicked'
              ? log.timestamp
                ? log.timestamp.toISOString()
                : new Date().toISOString()
              : null,
          clickedAt:
            normalizedStatus === 'clicked'
              ? log.timestamp
                ? log.timestamp.toISOString()
                : new Date().toISOString()
              : null,
          device: this.detectDevice(log.rawPayload),
          location: this.detectLocation(log.ipAddress),
          messageId: log.messageId || '',
        };
      });

      return {
        success: true,
        data: emailItems,
        total,
        skip: query.skip || 0,
        limit: query.limit || 50,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`INTERNAL_ERROR: ${error.message}`);
    }
  }

  /**
   * Calculate campaign statistics from webhook logs
   */
  private calculateCampaignStatistics(logs: any[]): CampaignOverviewStatsDTO {
    const stats = {
      totalEmails: logs.length,
      sent: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      bounced: 0,
      failed: 0,
      pending: 0,
    };

    // Count by status
    logs.forEach((log) => {
      const status = this.normalizeEmailStatus(log.status);
      switch (status) {
        case 'sent':
          stats.sent++;
          break;
        case 'delivered':
          stats.delivered++;
          break;
        case 'opened':
          stats.opened++;
          break;
        case 'clicked':
          stats.clicked++;
          break;
        case 'bounced':
          stats.bounced++;
          break;
        case 'failed':
          stats.failed++;
          break;
        case 'pending':
          stats.pending++;
          break;
      }
    });

    // Calculate rates
    const totalSent =
      stats.sent + stats.delivered + stats.opened + stats.clicked;

    return {
      ...stats,
      openRate:
        totalSent > 0
          ? Number(
              (((stats.opened + stats.clicked) / totalSent) * 100).toFixed(2),
            )
          : 0,
      clickRate:
        totalSent > 0
          ? Number(((stats.clicked / totalSent) * 100).toFixed(2))
          : 0,
      bounceRate:
        stats.totalEmails > 0
          ? Number(((stats.bounced / stats.totalEmails) * 100).toFixed(2))
          : 0,
      deliveryRate:
        stats.totalEmails > 0
          ? Number(
              (
                ((stats.delivered + stats.opened + stats.clicked) /
                  stats.totalEmails) *
                100
              ).toFixed(2),
            )
          : 0,
    };
  }

  /**
   * Get email subject from template
   */
  private getEmailSubjectFromTemplate(emailTemplate: any): string {
    if (!emailTemplate) {
      return 'Welcome to our service!';
    }

    // Try to get subject from different flows
    const flows = [
      ...(emailTemplate.startFlow || []),
      ...(emailTemplate.remindFlow || []),
      ...(emailTemplate.endFlow || []),
    ];

    const emailFlow = flows.find((flow) => flow.type === 'email');

    if (emailFlow && emailFlow.subject) {
      return emailFlow.subject;
    }

    return 'Welcome to our service!';
  }

  /**
   * Build email filters for query
   */
  private buildEmailFilters(query: MonitoringEmailsQueryDTO): any {
    const filters: any = {};

    // Status filter
    if (query.status) {
      const statusList = query.status.split(',').map((s) => s.trim());
      const normalizedStatuses = statusList.map((s) =>
        this.normalizeEmailStatus(s),
      );
      filters.status = { $in: normalizedStatuses };
    }

    // Search filter
    if (query.search) {
      const searchRegex = new RegExp(query.search, 'i');
      filters.$or = [{ email: searchRegex }, { subject: searchRegex }];
    }

    return filters;
  }

  /**
   * Build sort options for query
   */
  private buildSortOptions(sortBy?: string, sortOrder?: string): any {
    const field = sortBy || 'sentAt';
    const order = sortOrder === 'asc' ? 1 : -1;

    // Map frontend field names to database field names
    const fieldMapping = {
      sentAt: 'timestamp',
      email: 'email',
      status: 'status',
      openedAt: 'openedAt',
      clickedAt: 'clickedAt',
    };

    const dbField = fieldMapping[field] || 'timestamp';

    return { [dbField]: order };
  }

  /**
   * Normalize email status to standard values
   */
  private normalizeEmailStatus(status: string): string {
    if (!status) return 'pending';

    const statusLower = status.toLowerCase();

    // Map various status values to standard ones
    const statusMap = {
      // Standard statuses
      pending: 'pending',
      sent: 'sent',
      delivered: 'delivered',
      opened: 'opened',
      clicked: 'clicked',
      bounced: 'bounced',
      failed: 'failed',

      // AWS SES statuses
      send: 'sent',
      delivery: 'delivered',
      open: 'opened',
      click: 'clicked',
      bounce: 'bounced',
      reject: 'failed',
      complaint: 'failed',
      renderingfailure: 'failed',
      deliverydelay: 'pending',
    };

    return statusMap[statusLower] || 'pending';
  }
}
