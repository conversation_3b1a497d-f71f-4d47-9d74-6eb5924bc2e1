import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { isNil } from 'lodash';
import { CountryCodeFileShortLinkGuard } from 'src/shared/guards/country-code-file-short-link.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { CheckStatusShortLinkDTO } from '../short-link/dto/short-link.dto';
import {
  AddShortLinkMetaDTO,
  CreateFileShortLinksDTO,
  FileShortLinkResponseDTO,
  GetFileShortLinkQueryDTO,
  GetFileShortLinksQueryDTO,
  UpdateFileShortLinksDTO,
} from './dto/file-short-links.dto';
import { FileShortLinksService } from './file-short-links.service';

@ApiTags('Client File Short Links')
@UseGuards(GetEmailGuard)
@Controller('client/file-short-links')
export class ClientFileShortLinksController {
  constructor(private readonly fileShortLinkService: FileShortLinksService) {}

  @Get('/get-detail')
  async getDetailFileShortLink(@Query() query: GetFileShortLinkQueryDTO) {
    return this.fileShortLinkService.getFileShortLinkDetail(query);
  }

  @ApiBearerAuth('JWT-auth')
  @Get('')
  async getFiles(@Query() query: GetFileShortLinksQueryDTO) {
    return this.fileShortLinkService.getFileShortLinks(query);
  }

  @Get(':id/download')
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } =
      await this.fileShortLinkService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'attachment');

    stream.pipe(res);
  }

  @UseGuards(CountryCodeFileShortLinkGuard)
  @Get('check-status')
  async checkStatusShortLink(@Query() query: CheckStatusShortLinkDTO) {
    return this.fileShortLinkService.checkStatusShortLink(query.shortLinkId);
  }

  @Post('/create')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'file', maxCount: 1 }, // Attachment file
      { name: 'logo', maxCount: 1 }, // Logo
    ]),
  )
  async createFileShortLink(
    @Body() body: CreateFileShortLinksDTO,
    @UploadedFiles()
    files: { file?: Express.Multer.File[]; logo?: Express.Multer.File[] },
  ) {
    const file = files.file?.[0];
    const logo = files.logo?.[0];
    if (isNil(file)) {
      throw new BadRequestException('File is required');
    }
    return this.fileShortLinkService.createFileShortLink({
      createFileShortLinkDTO: body,
      file,
      logo,
    });
  }

  @Post('add-file-short-link-meta')
  @ApiOperation({
    summary: 'Add short link meta to existing file',
    description: 'Add new short link metas to an existing file short link',
  })
  @ApiResponse({
    status: 201,
    description: 'Short link metas added successfully',
    type: FileShortLinkResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'File short link not found',
  })
  async addShortLinkMeta(
    @Body() addShortLinkMetaDTO: AddShortLinkMetaDTO,
  ): Promise<FileShortLinkResponseDTO> {
    const { id, ...metaData } = addShortLinkMetaDTO;
    return this.fileShortLinkService.addShortLinkMeta(id, metaData);
  }
}
