import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  Max,
} from 'class-validator';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';

export class CreateFileShortLinksDTO {
  @IsString()
  @IsNotEmpty()
  prefixPass: string;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  popup?: boolean;

  @IsString()
  @IsNotEmpty()
  brand: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  expirationLink?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  countryCode?: string;

  @IsString()
  @IsNotEmpty()
  quantity: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  job?: string;
}

export class UpdateFileShortLinksDTO {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  prefixPass?: string;

  @IsBoolean()
  @IsOptional()
  popup?: boolean;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  brand?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  expirationLink?: string;

  @IsString()
  @IsOptional()
  countryCode?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  shortLinkStatus?: SHORT_LINK_STATUS;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  shortLinkUrl?: string;

  @IsString()
  @IsOptional()
  job?: string;
}

export class FileShortLinkMetaResponseDTO {
  @Expose()
  shortLinkId: string;

  @Expose()
  shortLinkStatus: SHORT_LINK_STATUS;

  @Expose()
  shortLinkUrl: string;

  @Expose()
  countryCode: string;
}

export class FileShortLinkResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  file: string;

  @Expose()
  logo: string;

  @Expose()
  prefixPass: string;

  @Expose()
  brand: string;

  @Expose()
  expirationLink: string;

  @Expose()
  shortLinkUrl: string;

  @Expose()
  job?: string;

  @Expose()
  @Type(() => FileShortLinkMetaResponseDTO)
  fileShortLinkMeta: FileShortLinkMetaResponseDTO[];

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}

export class GetFileShortLinkQueryDTO {
  @IsString()
  @IsNotEmpty()
  brand: string;

  @IsString()
  @IsNotEmpty()
  shortLinkId: string;
}

export class GetFileShortLinksQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by brand name',
    example: 'mybrand.com',
  })
  @IsString()
  @IsOptional()
  brand?: string;
}

export class GetFilesResponseDTO {
  @ApiProperty({
    description: 'Array of file short links',
    type: [FileShortLinkResponseDTO],
  })
  @Expose()
  @Type(() => FileShortLinkResponseDTO)
  data: FileShortLinkResponseDTO[];

  @ApiProperty({
    description: 'Total number of file short links',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class AddShortLinkMetaDTO {
  @ApiProperty({
    description: 'File short link ID',
    example: '64f1234567890abcdef12345',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Number of short link metas to add',
    example: 3,
    minimum: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({
    description: 'Enable popup for new short links',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  popup?: boolean;

  @ApiPropertyOptional({
    description: 'Country code for new short links',
    example: 'VN',
  })
  @IsString()
  @IsOptional()
  countryCode?: string;

  @ApiPropertyOptional({
    description: 'Short link status for new links',
    enum: SHORT_LINK_STATUS,
    example: SHORT_LINK_STATUS.ACTIVATED,
  })
  @IsEnum(SHORT_LINK_STATUS)
  @IsOptional()
  shortLinkStatus?: SHORT_LINK_STATUS;
}
