import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
  Max,
} from 'class-validator';
import { Expose } from 'class-transformer';
import {
  FLOW_TYPE,
  FLOW_ITEM_TYPE,
  PERIODIC_FREQUENCY,
} from '../../../shared/entities/camp/email-template.entity';

// Flow Item DTOs
export class EmailFlowItemDTO {
  @ApiProperty({
    description: 'Unique identifier for the flow item',
    example: 'email-001',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Email subject line',
    example: 'Welcome to our platform!',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Email content (HTML supported)',
    example: '<p>Hello {{name}}, welcome to our platform!</p>',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Flow type for this email',
    enum: FLOW_TYPE,
    example: FLOW_TYPE.START,
  })
  @IsEnum(FLOW_TYPE)
  flowType: FLOW_TYPE;

  @ApiProperty({
    description: 'Item type',
    enum: FLOW_ITEM_TYPE,
    example: FLOW_ITEM_TYPE.EMAIL,
  })
  @IsEnum(FLOW_ITEM_TYPE)
  type: FLOW_ITEM_TYPE;

  @ApiPropertyOptional({
    description: 'Delay in seconds before sending this email',
    example: 86400,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  delay?: number;

  @ApiPropertyOptional({
    description: 'Required fields for this email',
    type: [String],
    example: ['USERNAME', 'EMAIL', 'COMPANYNAME'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requirements?: string[];
}

export class DelayFlowItemDTO {
  @ApiProperty({
    description: 'Unique identifier for the delay item',
    example: 'delay-001',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Delay in seconds',
    example: 86400,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  delay: number;

  @ApiProperty({
    description: 'Item type',
    enum: FLOW_ITEM_TYPE,
    example: FLOW_ITEM_TYPE.DELAY,
  })
  @IsEnum(FLOW_ITEM_TYPE)
  type: FLOW_ITEM_TYPE;
}

// Periodic Config DTO
export class PeriodicConfigDTO {
  @ApiPropertyOptional({
    description: 'Periodic campaign start date',
    example: '2024-02-01T00:00:00Z',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Periodic campaign end date',
    example: '2025-02-01T00:00:00Z',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Frequency of periodic emails',
    enum: PERIODIC_FREQUENCY,
    example: PERIODIC_FREQUENCY.WEEKLY,
  })
  @IsEnum(PERIODIC_FREQUENCY)
  @IsOptional()
  frequency?: PERIODIC_FREQUENCY;

  @ApiPropertyOptional({
    description: 'Maximum number of periodic emails to send',
    example: 12,
  })
  @IsNumber()
  @IsOptional()
  maxSendCount?: number;

  @ApiPropertyOptional({
    description: 'Whether periodic emails are enabled',
    example: true,
  })
  @IsOptional()
  enabled?: boolean;
}

// Form Data DTOs for file upload
export class CreateEmailTemplateFormDataDTO {
  @ApiProperty({
    description: 'Template name',
    example: 'Welcome Series Template',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Template description',
    example: 'Automated welcome email series for new users',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Sender email address',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  sender: string;

  @ApiProperty({
    description: 'Sender display name',
    example: 'Welcome Team',
  })
  @IsString()
  @IsNotEmpty()
  senderName: string;

  @ApiProperty({
    description: 'Brand identifier',
    example: 'company-brand',
  })
  @IsString()
  @IsNotEmpty()
  brand: string;

  @ApiProperty({
    description: 'Domain for email links',
    example: 'company.com',
  })
  @IsString()
  @IsNotEmpty()
  domain: string;

  @ApiProperty({
    description: 'URL prefix for tracking',
    example: 'welcome',
  })
  @IsString()
  @IsNotEmpty()
  prefix: string;

  @ApiPropertyOptional({
    description: 'Expiration link URL',
    example: 'https://company.com/expired',
  })
  @IsString()
  @IsOptional()
  expirationLink?: string;

  @ApiPropertyOptional({
    description: 'Start flow items as JSON string',
    example:
      '[{"id":"start-email-1","subject":"Welcome!","content":"<p>Welcome!</p>","flowType":"start","type":"email"}]',
  })
  @IsString()
  @IsOptional()
  startFlow?: string;

  @ApiPropertyOptional({
    description: 'Remind flow items as JSON string',
    example:
      '[{"id":"remind-email-1","subject":"Reminder","content":"<p>Don\'t forget!</p>","flowType":"remind","type":"email"}]',
  })
  @IsString()
  @IsOptional()
  remindFlow?: string;

  @ApiPropertyOptional({
    description: 'End flow items as JSON string',
    example:
      '[{"id":"end-email-1","subject":"Thank you","content":"<p>Thank you!</p>","flowType":"end","type":"email"}]',
  })
  @IsString()
  @IsOptional()
  endFlow?: string;

  @ApiPropertyOptional({
    description: 'Periodic flow items as JSON string',
    example:
      '[{"id":"periodic-email-1","subject":"Newsletter","content":"<p>Newsletter content</p>","flowType":"periodic","type":"email"}]',
  })
  @IsString()
  @IsOptional()
  periodicFlow?: string;

  @ApiPropertyOptional({
    description: 'Periodic configuration as JSON string',
    example:
      '{"startDate":"2024-02-01T00:00:00Z","endDate":"2025-02-01T00:00:00Z","frequency":"weekly","maxSendCount":12,"enabled":true}',
  })
  @IsString()
  @IsOptional()
  periodicConfig?: string;

  @ApiPropertyOptional({
    description: 'Template start date',
    example: '2024-12-20T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Template end date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

export class UpdateEmailTemplateFormDataDTO {
  @ApiPropertyOptional({
    description: 'Template name',
    example: 'Updated Welcome Series',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Template description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Sender email',
  })
  @IsString()
  @IsOptional()
  sender?: string;

  @ApiPropertyOptional({
    description: 'Sender name',
  })
  @IsString()
  @IsOptional()
  senderName?: string;

  @ApiPropertyOptional({
    description: 'Brand',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiPropertyOptional({
    description: 'Domain',
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiPropertyOptional({
    description: 'Prefix',
  })
  @IsString()
  @IsOptional()
  prefix?: string;

  @ApiPropertyOptional({
    description: 'Expiration link URL',
    example: 'https://company.com/expired',
  })
  @IsString()
  @IsOptional()
  expirationLink?: string;

  @ApiPropertyOptional({
    description: 'Start flow as JSON string',
  })
  @IsString()
  @IsOptional()
  startFlow?: string;

  @ApiPropertyOptional({
    description: 'Remind flow as JSON string',
  })
  @IsString()
  @IsOptional()
  remindFlow?: string;

  @ApiPropertyOptional({
    description: 'End flow as JSON string',
  })
  @IsString()
  @IsOptional()
  endFlow?: string;

  @ApiPropertyOptional({
    description: 'Periodic flow as JSON string',
  })
  @IsString()
  @IsOptional()
  periodicFlow?: string;

  @ApiPropertyOptional({
    description: 'Periodic configuration as JSON string',
    example:
      '{"startDate":"2024-02-01T00:00:00Z","endDate":"2025-02-01T00:00:00Z","frequency":"weekly","maxSendCount":12,"enabled":true}',
  })
  @IsString()
  @IsOptional()
  periodicConfig?: string;

  // Status management moved to Camp entity

  @ApiPropertyOptional({
    description: 'Start date',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

// Create Email Template DTO
export class CreateEmailTemplateDTO {
  @ApiProperty({
    description: 'Template name',
    example: 'Welcome Series Template',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Campaign description',
    example: 'Automated welcome email series for new users',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Sender email address',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  sender: string;

  @ApiProperty({
    description: 'Sender display name',
    example: 'Welcome Team',
  })
  @IsString()
  @IsNotEmpty()
  senderName: string;

  @ApiProperty({
    description: 'Brand identifier',
    example: 'company-brand',
  })
  @IsString()
  @IsNotEmpty()
  brand: string;

  @ApiProperty({
    description: 'Domain for email links',
    example: 'company.com',
  })
  @IsString()
  @IsNotEmpty()
  domain: string;

  @ApiProperty({
    description: 'URL prefix for tracking',
    example: 'welcome',
  })
  @IsString()
  @IsNotEmpty()
  prefix: string;

  @ApiPropertyOptional({
    description: 'Associated file URL',
    example: 'https://storage.com/welcome-guide.pdf',
  })
  @IsString()
  @IsOptional()
  file?: string;

  @ApiPropertyOptional({
    description: 'Required columns in user CSV file for validation',
    example: ['EMAIL', 'COUNTRY', 'FULLNAME', 'JOB'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requirements?: string[];

  @ApiPropertyOptional({
    description: 'Expiration link URL',
    example: 'https://company.com/expired',
  })
  @IsString()
  @IsOptional()
  expirationLink?: string;

  @ApiPropertyOptional({
    description: 'Start flow items (emails and delays)',
    type: [Object],
    example: [
      {
        id: 'start-email-1',
        subject: 'Welcome!',
        content: '<p>Welcome {{name}}!</p>',
        flowType: 'start',
        type: 'email',
      },
      {
        id: 'delay-1',
        delay: 86400,
        type: 'delay',
      },
    ],
  })
  @IsArray()
  @IsOptional()
  startFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Remind flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  remindFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'End flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  endFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Periodic flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  periodicFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Periodic configuration for automated recurring emails',
    type: PeriodicConfigDTO,
  })
  @Type(() => PeriodicConfigDTO)
  @IsOptional()
  periodicConfig?: PeriodicConfigDTO;

  @ApiPropertyOptional({
    description: 'Template start date',
    example: '2024-12-20T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Template end date',
    example: '2024-12-31T23:59:59.000Z',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

// Update Email Template DTO
export class UpdateEmailTemplateDTO {
  @ApiProperty({
    description: 'Template ID',
    example: '64f1234567890abcdef12345',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiPropertyOptional({
    description: 'Template name',
    example: 'Updated Welcome Series',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Template description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Sender email',
  })
  @IsString()
  @IsOptional()
  sender?: string;

  @ApiPropertyOptional({
    description: 'Sender name',
  })
  @IsString()
  @IsOptional()
  senderName?: string;

  @ApiPropertyOptional({
    description: 'Brand',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiPropertyOptional({
    description: 'Domain',
  })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiPropertyOptional({
    description: 'Prefix',
  })
  @IsString()
  @IsOptional()
  prefix?: string;

  @ApiPropertyOptional({
    description: 'File URL',
  })
  @IsString()
  @IsOptional()
  file?: string;

  @ApiPropertyOptional({
    description: 'Expiration link URL',
    example: 'https://company.com/expired',
  })
  @IsString()
  @IsOptional()
  expirationLink?: string;

  @ApiPropertyOptional({
    description: 'Start flow items (emails and delays)',
    type: [Object],
    example: [
      {
        id: 'start-email-1',
        subject: 'Welcome!',
        content: '<p>Welcome {{name}}!</p>',
        flowType: 'start',
        type: 'email',
      },
      {
        id: 'delay-1',
        delay: 86400,
        type: 'delay',
      },
    ],
  })
  @IsArray()
  @IsOptional()
  startFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Remind flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  remindFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'End flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  endFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Periodic flow items',
    type: [Object],
  })
  @IsArray()
  @IsOptional()
  periodicFlow?: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Periodic configuration for automated recurring emails',
    type: PeriodicConfigDTO,
  })
  @Type(() => PeriodicConfigDTO)
  @IsOptional()
  periodicConfig?: PeriodicConfigDTO;

  // Status management moved to Camp entity

  @ApiPropertyOptional({
    description: 'Start date',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

// Query DTOs
export class GetEmailTemplatesQueryDTO {
  @ApiPropertyOptional({
    description: 'Page number',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search by campaign name',
    example: 'welcome',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by brand',
    example: 'company-brand',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  // Status filtering moved to Camp entity

  @ApiPropertyOptional({
    description: 'Filter by start date (from)',
    example: '2024-12-01',
  })
  @IsDateString()
  @IsOptional()
  startDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by start date (to)',
    example: '2024-12-31',
  })
  @IsDateString()
  @IsOptional()
  startDateTo?: string;
}

// Response DTOs
export class EmailTemplateResponseDTO {
  @ApiProperty({ description: 'Template ID' })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({ description: 'Template name' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'Template description' })
  @Expose()
  description?: string;

  @ApiProperty({ description: 'Sender email' })
  @Expose()
  sender: string;

  @ApiProperty({ description: 'Sender name' })
  @Expose()
  senderName: string;

  @ApiProperty({ description: 'Brand' })
  @Expose()
  brand: string;

  @ApiProperty({ description: 'Domain' })
  @Expose()
  domain: string;

  @ApiProperty({ description: 'Prefix' })
  @Expose()
  prefix: string;

  @ApiProperty({ description: 'File URL' })
  @Expose()
  file?: string;

  @ApiPropertyOptional({
    description: 'Required columns in user CSV file for validation',
    example: ['EMAIL', 'COUNTRY', 'FULLNAME', 'JOB'],
    type: [String],
  })
  @Expose()
  requirements?: string[];

  @ApiPropertyOptional({
    description: 'Expiration link URL',
    example: 'https://company.com/expired',
  })
  @Expose()
  expirationLink?: string;

  @ApiProperty({ description: 'Start flow items' })
  @Expose()
  startFlow: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiProperty({ description: 'Remind flow items' })
  @Expose()
  remindFlow: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiProperty({ description: 'End flow items' })
  @Expose()
  endFlow: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiProperty({ description: 'Periodic flow items' })
  @Expose()
  periodicFlow: Array<EmailFlowItemDTO | DelayFlowItemDTO>;

  @ApiPropertyOptional({
    description: 'Periodic configuration for automated recurring emails',
    type: PeriodicConfigDTO,
  })
  @Expose()
  @Type(() => PeriodicConfigDTO)
  periodicConfig?: PeriodicConfigDTO;

  // Status field removed - moved to Camp entity

  @ApiProperty({ description: 'Start date' })
  @Expose()
  startDate?: Date;

  @ApiProperty({ description: 'End date' })
  @Expose()
  endDate?: Date;

  @ApiProperty({ description: 'Total subscribers' })
  @Expose()
  totalSubscribers: number;

  @ApiProperty({ description: 'Total emails sent' })
  @Expose()
  totalEmailsSent: number;

  @ApiProperty({ description: 'Total opened' })
  @Expose()
  totalOpened: number;

  @ApiProperty({ description: 'Total clicked' })
  @Expose()
  totalClicked: number;

  @ApiProperty({ description: 'Created date' })
  @Expose()
  createdAt: Date;

  @ApiProperty({ description: 'Created by username' })
  @Expose()
  createdByUsername?: string;

  @ApiProperty({ description: 'Updated date' })
  @Expose()
  updatedAt?: Date;
}

export class GetEmailTemplatesResponseDTO {
  @ApiProperty({
    description: 'List of campaigns',
    type: [EmailTemplateResponseDTO],
  })
  @Expose()
  @Type(() => EmailTemplateResponseDTO)
  data: EmailTemplateResponseDTO[];

  @ApiProperty({ description: 'Total count' })
  @Expose()
  total: number;

  @ApiProperty({ description: 'Current page' })
  @Expose()
  page: number;

  @ApiProperty({ description: 'Items per page' })
  @Expose()
  limit: number;

  @ApiProperty({ description: 'Total pages' })
  @Expose()
  totalPages: number;
}
