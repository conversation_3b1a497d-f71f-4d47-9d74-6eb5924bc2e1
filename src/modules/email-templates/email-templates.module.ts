import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { AdminDB } from '../../config';
import {
  ValidToken,
  ValidTokenSchema,
} from '../../shared/entities/auth/valid-token.entity';
import {
  EmailTemplate,
  EmailTemplateSchema,
} from '../../shared/entities/camp/email-template.entity';
import { EmailTemplatesController } from './email-templates.controller';
import { EmailTemplatesService } from './email-templates.service';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature(
      [
        {
          name: EmailTemplate.name,
          schema: EmailTemplateSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
  ],
  controllers: [EmailTemplatesController],
  providers: [EmailTemplatesService, AzureStorageService],
  exports: [EmailTemplatesService],
})
export class EmailTemplatesModule {}
