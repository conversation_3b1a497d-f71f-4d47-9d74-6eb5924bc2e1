import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RequestUser } from '../../shared/decorators/request-user.decorator';
import { User } from '../../shared/entities/auth/user.entity';
import { AuthGuard } from '../../shared/guards/auth.guard';
import {
  CreateEmailTemplateFormDataDTO,
  EmailTemplateResponseDTO,
  GetEmailTemplatesQueryDTO,
  GetEmailTemplatesResponseDTO,
  UpdateEmailTemplateFormDataDTO,
} from './dto/email-templates.dto';
import { EmailTemplatesService } from './email-templates.service';

@ApiTags('Email Templates')
@Controller('email-templates')
@ApiBearerAuth('JWT-auth')
@UseGuards(AuthGuard)
export class EmailTemplatesController {
  constructor(private readonly emailTemplatesService: EmailTemplatesService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create email template with file upload',
    description:
      'Create a new email template with flows and optional file upload',
  })
  @ApiBody({
    description: 'Template data with optional file',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Welcome Series Template' },
        description: {
          type: 'string',
          example: 'Automated welcome email series',
        },
        sender: { type: 'string', example: '<EMAIL>' },
        senderName: { type: 'string', example: 'Welcome Team' },
        brand: { type: 'string', example: 'company-brand' },
        domain: { type: 'string', example: 'company.com' },
        prefix: { type: 'string', example: 'welcome' },
        startFlow: {
          type: 'string',
          example:
            '[{"id":"start-email-1","subject":"Welcome!","content":"<p>Welcome!</p>","flowType":"start","type":"email"}]',
          description: 'JSON string of start flow items',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional file upload',
        },
      },
      required: ['name', 'sender', 'senderName', 'brand', 'domain', 'prefix'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Email template created successfully',
    type: EmailTemplateResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid template data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async createEmailTemplate(
    @Body() createEmailTemplateDto: CreateEmailTemplateFormDataDTO,
    @UploadedFile() file: Express.Multer.File,
    @RequestUser() user: User,
  ): Promise<EmailTemplateResponseDTO> {
    if (!file) {
      throw new BadRequestException(
        'File upload is required for creating an email template',
      );
    }

    return this.emailTemplatesService.createEmailTemplateWithFile(
      createEmailTemplateDto,
      file,
      user,
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Get email templates',
    description: 'Get all email templates with pagination and filters',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, description: 'Search by name' })
  @ApiQuery({ name: 'brand', required: false, description: 'Filter by brand' })
  @ApiQuery({
    name: 'startDateFrom',
    required: false,
    description: 'Filter by start date from',
  })
  @ApiQuery({
    name: 'startDateTo',
    required: false,
    description: 'Filter by start date to',
  })
  @ApiResponse({
    status: 200,
    description: 'Email templates retrieved successfully',
    type: GetEmailTemplatesResponseDTO,
  })
  async getEmailTemplates(
    @Query() query: GetEmailTemplatesQueryDTO,
    @RequestUser() user: User,
  ): Promise<GetEmailTemplatesResponseDTO> {
    return this.emailTemplatesService.getEmailTemplates(query);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get email template by ID',
    description: 'Get a specific email template by its ID',
  })
  @ApiParam({ name: 'id', description: 'Template ID' })
  @ApiResponse({
    status: 200,
    description: 'Email template retrieved successfully',
    type: EmailTemplateResponseDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Email template not found',
  })
  async getEmailTemplateById(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<EmailTemplateResponseDTO> {
    return this.emailTemplatesService.getEmailTemplateById(id);
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update email campaign with file upload',
    description: 'Update an existing email campaign with optional file upload',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiBody({
    description: 'Campaign update data with optional file',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Updated Welcome Series' },
        description: { type: 'string', example: 'Updated description' },
        sender: { type: 'string', example: '<EMAIL>' },
        senderName: { type: 'string', example: 'Welcome Team' },
        brand: { type: 'string', example: 'company-brand' },
        domain: { type: 'string', example: 'company.com' },
        prefix: { type: 'string', example: 'welcome' },
        startFlow: {
          type: 'string',
          example:
            '[{"id":"start-email-1","subject":"Welcome!","content":"<p>Welcome!</p>","flowType":"start","type":"email"}]',
          description: 'JSON string of start flow items',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional file upload',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Email campaign updated successfully',
    type: EmailTemplateResponseDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Email campaign not found',
  })
  async updateEmailTemplate(
    @Param('id') id: string,
    @Body() updateEmailTemplateDto: UpdateEmailTemplateFormDataDTO,
    @UploadedFile() file: Express.Multer.File,
    @RequestUser() user: User,
  ): Promise<EmailTemplateResponseDTO> {
    return this.emailTemplatesService.updateEmailTemplateWithFile(
      id,
      updateEmailTemplateDto,
      file,
      user,
    );
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete email campaign',
    description: 'Soft delete an email campaign',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({
    status: 200,
    description: 'Email campaign deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Email campaign not found',
  })
  async deleteEmailTemplate(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{ message: string }> {
    return this.emailTemplatesService.deleteEmailTemplate(id, user);
  }

  @Get(':id/statistics')
  @ApiOperation({
    summary: 'Get campaign statistics',
    description: 'Get detailed statistics for an email campaign',
  })
  @ApiParam({ name: 'id', description: 'Campaign ID' })
  @ApiResponse({
    status: 200,
    description: 'Campaign statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalSubscribers: { type: 'number', description: 'Total subscribers' },
        totalEmailsSent: { type: 'number', description: 'Total emails sent' },
        totalOpened: { type: 'number', description: 'Total emails opened' },
        totalClicked: { type: 'number', description: 'Total emails clicked' },
        openRate: { type: 'number', description: 'Open rate percentage' },
        clickRate: { type: 'number', description: 'Click rate percentage' },
        clickThroughRate: {
          type: 'number',
          description: 'Click-through rate percentage',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Email campaign not found',
  })
  async getCampaignStatistics(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    totalSubscribers: number;
    totalEmailsSent: number;
    totalOpened: number;
    totalClicked: number;
    openRate: number;
    clickRate: number;
    clickThroughRate: number;
  }> {
    return this.emailTemplatesService.getTemplateStatistics(id);
  }

  @Get(':id/download')
  @ApiOperation({
    summary: 'Download email template file',
    description: 'Download the file associated with an email template',
  })
  @ApiParam({
    name: 'id',
    description: 'Email template ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Email template not found or no file associated',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 404,
    description: 'Email template not found',
  })
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType, filename } =
      await this.emailTemplatesService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${filename || 'template-file'}"`,
    );

    stream.pipe(res);
  }
}
