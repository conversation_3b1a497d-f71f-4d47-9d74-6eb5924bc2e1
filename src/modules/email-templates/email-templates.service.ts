import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { Model } from 'mongoose';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { AdminDB } from '../../config';
import { User } from '../../shared/entities/auth/user.entity';
import {
  EmailTemplate,
  EmailTemplateDocument,
  FLOW_ITEM_TYPE,
} from '../../shared/entities/camp/email-template.entity';
import {
  CreateEmailTemplateDTO,
  CreateEmailTemplateFormDataDTO,
  EmailTemplateResponseDTO,
  GetEmailTemplatesQueryDTO,
  GetEmailTemplatesResponseDTO,
  UpdateEmailTemplateDTO,
  UpdateEmailTemplateFormDataDTO,
} from './dto/email-templates.dto';

@Injectable()
export class EmailTemplatesService {
  constructor(
    @InjectModel(EmailTemplate.name, AdminDB)
    private readonly emailTemplateModel: Model<EmailTemplateDocument>,

    private readonly azureStorageService: AzureStorageService,
  ) {}

  /**
   * Create a new email template with form data and file upload
   */
  async createEmailTemplateWithFile(
    createEmailTemplateDto: CreateEmailTemplateFormDataDTO,
    file: Express.Multer.File | undefined,
    authUser: User,
  ): Promise<EmailTemplateResponseDTO> {
    try {
      // Parse JSON flows from form data
      const parsedData = this.parseFormDataFlows(createEmailTemplateDto);

      // Upload file if provided
      let fileUrl: string | undefined;
      if (file) {
        fileUrl = await this.uploadFile(file);
      }

      // Create campaign with parsed data and file URL
      const campaignData = {
        ...parsedData,
        file: fileUrl,
        // status moved to Camp entity
        totalSubscribers: 0,
        totalEmailsSent: 0,
        totalOpened: 0,
        totalClicked: 0,
        createdAt: new Date(),
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        startDate: parsedData.startDate
          ? new Date(parsedData.startDate)
          : undefined,
        endDate: parsedData.endDate ? new Date(parsedData.endDate) : undefined,
      };

      const newCampaign = new this.emailTemplateModel(campaignData);
      const savedCampaign = await newCampaign.save();

      return plainToInstance(
        EmailTemplateResponseDTO,
        savedCampaign.toObject(),
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create email template: ${error.message}`,
      );
    }
  }

  /**
   * Create a new email template
   */
  async createEmailTemplate(
    createEmailTemplateDto: CreateEmailTemplateDTO,
    authUser: User,
  ): Promise<EmailTemplateResponseDTO> {
    try {
      // Validate flow items
      this.validateFlowItems(createEmailTemplateDto.startFlow || []);
      this.validateFlowItems(createEmailTemplateDto.remindFlow || []);
      this.validateFlowItems(createEmailTemplateDto.endFlow || []);
      this.validateFlowItems(createEmailTemplateDto.periodicFlow || []);

      // Create campaign data
      const campaignData = {
        ...createEmailTemplateDto,
        // status moved to Camp entity
        totalSubscribers: 0,
        totalEmailsSent: 0,
        totalOpened: 0,
        totalClicked: 0,
        createdAt: new Date(),
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        startDate: createEmailTemplateDto.startDate
          ? new Date(createEmailTemplateDto.startDate)
          : undefined,
        endDate: createEmailTemplateDto.endDate
          ? new Date(createEmailTemplateDto.endDate)
          : undefined,
        periodicConfig: createEmailTemplateDto.periodicConfig
          ? {
              ...createEmailTemplateDto.periodicConfig,
              startDate: createEmailTemplateDto.periodicConfig.startDate
                ? new Date(createEmailTemplateDto.periodicConfig.startDate)
                : undefined,
              endDate: createEmailTemplateDto.periodicConfig.endDate
                ? new Date(createEmailTemplateDto.periodicConfig.endDate)
                : undefined,
            }
          : undefined,
      };

      const newCampaign = new this.emailTemplateModel(campaignData);
      const savedCampaign = await newCampaign.save();

      return plainToInstance(
        EmailTemplateResponseDTO,
        savedCampaign.toObject(),
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      console.log('Error creating email template:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create email template: ${error.message}`,
      );
    }
  }

  /**
   * Get all email templates with pagination and filters
   */
  async getEmailTemplates(
    query: GetEmailTemplatesQueryDTO,
  ): Promise<GetEmailTemplatesResponseDTO> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        brand,
        startDateFrom,
        startDateTo,
      } = query;
      const skip = (page - 1) * limit;

      // Build filter conditions
      const filter: any = {};

      if (search) {
        filter.name = { $regex: search, $options: 'i' };
      }

      if (brand) {
        filter.brand = brand;
      }

      if (startDateFrom || startDateTo) {
        filter.startDate = {};
        if (startDateFrom) {
          filter.startDate.$gte = new Date(startDateFrom);
        }
        if (startDateTo) {
          filter.startDate.$lte = new Date(startDateTo);
        }
      }

      // Execute queries
      const [campaigns, total] = await Promise.all([
        this.emailTemplateModel
          .find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        this.emailTemplateModel.countDocuments(filter),
      ]);

      const campaignResponses = campaigns.map((campaign) =>
        plainToInstance(EmailTemplateResponseDTO, campaign, {
          excludeExtraneousValues: true,
        }),
      );

      return {
        data: campaignResponses,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to get email templates: ${error.message}`,
      );
    }
  }

  /**
   * Get email template by ID
   */
  async getEmailTemplateById(id: string): Promise<EmailTemplateResponseDTO> {
    try {
      const template = await this.emailTemplateModel.findById(id).lean();

      if (!template) {
        throw new NotFoundException('email template not found');
      }

      return plainToInstance(EmailTemplateResponseDTO, template, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get email template: ${error.message}`,
      );
    }
  }

  /**
   * Update email template with form data and file upload
   */
  async updateEmailTemplateWithFile(
    id: string,
    updateEmailTemplateDto: UpdateEmailTemplateFormDataDTO,
    file: Express.Multer.File | undefined,
    authUser: User,
  ): Promise<EmailTemplateResponseDTO> {
    try {
      // Check if campaign exists
      const existingTemplate = await this.emailTemplateModel.findById(id);
      if (!existingTemplate) {
        throw new NotFoundException('Email template not found');
      }

      // Parse JSON flows from form data
      const parsedData = this.parseFormDataFlows(updateEmailTemplateDto);

      // Upload file if provided
      let fileUrl: string | undefined = existingTemplate.file;
      if (file) {
        fileUrl = await this.uploadFile(file);
      }

      // Prepare update data
      const updatePayload: any = {
        ...parsedData,
        file: fileUrl,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      };

      // Convert date strings to Date objects
      if (parsedData.startDate) {
        updatePayload.startDate = new Date(parsedData.startDate);
      }
      if (parsedData.endDate) {
        updatePayload.endDate = new Date(parsedData.endDate);
      }
      if (parsedData.periodicConfig) {
        updatePayload.periodicConfig = {
          ...parsedData.periodicConfig,
          startDate: parsedData.periodicConfig.startDate
            ? new Date(parsedData.periodicConfig.startDate)
            : undefined,
          endDate: parsedData.periodicConfig.endDate
            ? new Date(parsedData.periodicConfig.endDate)
            : undefined,
        };
      }

      const updatedCampaign = await this.emailTemplateModel
        .findByIdAndUpdate(id, updatePayload, { new: true })
        .lean();

      return plainToInstance(EmailTemplateResponseDTO, updatedCampaign, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log('Error updating email template:', error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update email template: ${error.message}`,
      );
    }
  }

  /**
   * Update email template
   */
  async updateEmailTemplate(
    updateEmailTemplateDto: UpdateEmailTemplateDTO,
    authUser: User,
  ): Promise<EmailTemplateResponseDTO> {
    try {
      const { id, ...updateData } = updateEmailTemplateDto;

      // Check if template exists
      const existingTemplate = await this.emailTemplateModel.findById(id);
      if (!existingTemplate) {
        throw new NotFoundException('email template not found');
      }

      // Validate flow items if provided
      if (updateData.startFlow) {
        this.validateFlowItems(updateData.startFlow);
      }
      if (updateData.remindFlow) {
        this.validateFlowItems(updateData.remindFlow);
      }
      if (updateData.endFlow) {
        this.validateFlowItems(updateData.endFlow);
      }
      if (updateData.periodicFlow) {
        this.validateFlowItems(updateData.periodicFlow);
      }

      // Prepare update data
      const updatePayload: any = {
        ...updateData,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      };

      // Convert date strings to Date objects
      if (updateData.startDate) {
        updatePayload.startDate = new Date(updateData.startDate);
      }
      if (updateData.endDate) {
        updatePayload.endDate = new Date(updateData.endDate);
      }
      if (updateData.periodicConfig) {
        updatePayload.periodicConfig = {
          ...updateData.periodicConfig,
          startDate: updateData.periodicConfig.startDate
            ? new Date(updateData.periodicConfig.startDate)
            : undefined,
          endDate: updateData.periodicConfig.endDate
            ? new Date(updateData.periodicConfig.endDate)
            : undefined,
        };
      }

      const updatedCampaign = await this.emailTemplateModel
        .findByIdAndUpdate(id, updatePayload, { new: true })
        .lean();

      return plainToInstance(EmailTemplateResponseDTO, updatedCampaign, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update email template: ${error.message}`,
      );
    }
  }

  /**
   * Delete email template (soft delete)
   */
  async deleteEmailTemplate(
    id: string,
    authUser: User,
  ): Promise<{ message: string }> {
    try {
      const template = await this.emailTemplateModel.findById(id);
      if (!template) {
        throw new NotFoundException('email template not found');
      }

      await this.emailTemplateModel.findByIdAndUpdate(id, {
        deletedAt: new Date(),
        deletedBy: authUser.telegramId,
      });

      return { message: 'email template deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete email template: ${error.message}`,
      );
    }
  }

  /**
   * Update campaign status - DEPRECATED: Status moved to Camp entity
   */
  // Status management moved to Camp entity - method removed

  /**
   * Get template statistics
   */
  async getTemplateStatistics(id: string): Promise<{
    totalSubscribers: number;
    totalEmailsSent: number;
    totalOpened: number;
    totalClicked: number;
    openRate: number;
    clickRate: number;
    clickThroughRate: number;
  }> {
    try {
      const template = await this.emailTemplateModel.findById(id).lean();
      if (!template) {
        throw new NotFoundException('email template not found');
      }

      const openRate =
        template.totalEmailsSent > 0
          ? (template.totalOpened / template.totalEmailsSent) * 100
          : 0;

      const clickRate =
        template.totalEmailsSent > 0
          ? (template.totalClicked / template.totalEmailsSent) * 100
          : 0;

      const clickThroughRate =
        template.totalOpened > 0
          ? (template.totalClicked / template.totalOpened) * 100
          : 0;

      return {
        totalSubscribers: template.totalSubscribers || 0,
        totalEmailsSent: template.totalEmailsSent || 0,
        totalOpened: template.totalOpened || 0,
        totalClicked: template.totalClicked || 0,
        openRate: Math.round(openRate * 100) / 100,
        clickRate: Math.round(clickRate * 100) / 100,
        clickThroughRate: Math.round(clickThroughRate * 100) / 100,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to get campaign statistics: ${error.message}`,
      );
    }
  }

  /**
   * Validate flow items structure
   */
  private validateFlowItems(flowItems: any[]): void {
    for (const item of flowItems) {
      if (!item.id || !item.type) {
        throw new BadRequestException('Flow item must have id and type');
      }

      if (item.type === FLOW_ITEM_TYPE.EMAIL) {
        if (!item.subject || !item.content || !item.flowType) {
          throw new BadRequestException(
            'Email flow item must have subject, content, and flowType',
          );
        }
        // Validate requirements if provided
        if (item.requirements && !Array.isArray(item.requirements)) {
          throw new BadRequestException(
            'Email flow item requirements must be an array of strings',
          );
        }
      } else if (item.type === FLOW_ITEM_TYPE.DELAY) {
        if (typeof item.delay !== 'number' || item.delay < 0) {
          throw new BadRequestException(
            'Delay flow item must have a valid delay number',
          );
        }
      } else {
        throw new BadRequestException(`Invalid flow item type: ${item.type}`);
      }
    }
  }

  /**
   * Validate status transition - DEPRECATED: Status moved to Camp entity
   */
  // Status validation moved to Camp entity - method removed

  /**
   * Parse JSON flows from form data
   */
  private parseFormDataFlows(
    formData: CreateEmailTemplateFormDataDTO | UpdateEmailTemplateFormDataDTO,
  ): any {
    const parsedData: any = { ...formData };

    // Parse JSON strings to objects
    if (formData.startFlow) {
      try {
        parsedData.startFlow = JSON.parse(formData.startFlow);
        this.validateFlowItems(parsedData.startFlow);
      } catch (error) {
        console.log('Error parsing startFlow JSON:', error);
        throw new BadRequestException('Invalid startFlow JSON format');
      }
    }

    if (formData.remindFlow) {
      try {
        parsedData.remindFlow = JSON.parse(formData.remindFlow);
        this.validateFlowItems(parsedData.remindFlow);
      } catch (error) {
        throw new BadRequestException('Invalid remindFlow JSON format');
      }
    }

    if (formData.endFlow) {
      try {
        parsedData.endFlow = JSON.parse(formData.endFlow);
        this.validateFlowItems(parsedData.endFlow);
      } catch (error) {
        throw new BadRequestException('Invalid endFlow JSON format');
      }
    }

    if (formData.periodicFlow) {
      try {
        parsedData.periodicFlow = JSON.parse(formData.periodicFlow);
        this.validateFlowItems(parsedData.periodicFlow);
      } catch (error) {
        throw new BadRequestException('Invalid periodicFlow JSON format');
      }
    }

    if ((formData as any).periodicConfig) {
      try {
        const parsedPeriodicConfig = JSON.parse(
          (formData as any).periodicConfig,
        );
        parsedData.periodicConfig = {
          ...parsedPeriodicConfig,
          startDate: parsedPeriodicConfig.startDate
            ? new Date(parsedPeriodicConfig.startDate)
            : undefined,
          endDate: parsedPeriodicConfig.endDate
            ? new Date(parsedPeriodicConfig.endDate)
            : undefined,
        };
      } catch (error) {
        throw new BadRequestException('Invalid periodicConfig JSON format');
      }
    }

    return parsedData;
  }

  /**
   * Upload file to storage and return URL
   */
  private async uploadFile(file: Express.Multer.File): Promise<string> {
    try {
      return this.azureStorageService.uploadFile(file);
    } catch (error) {
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Validate CSV file columns against template requirements
   */
  async validateCsvRequirements(
    templateId: string,
    csvColumns: string[],
  ): Promise<{
    isValid: boolean;
    missingColumns: string[];
    extraColumns: string[];
  }> {
    try {
      const template = await this.emailTemplateModel
        .findById(templateId)
        .lean();
      if (!template) {
        throw new NotFoundException('Email template not found');
      }

      const requiredColumns = template.requirements || [
        'EMAIL',
        'COUNTRY',
        'FULLNAME',
        'JOB',
      ];
      const csvColumnsUpper = csvColumns.map((col) => col.toUpperCase());

      const missingColumns = requiredColumns.filter(
        (required) => !csvColumnsUpper.includes(required.toUpperCase()),
      );

      const extraColumns = csvColumnsUpper.filter(
        (col) => !requiredColumns.map((req) => req.toUpperCase()).includes(col),
      );

      return {
        isValid: missingColumns.length === 0,
        missingColumns,
        extraColumns,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to validate CSV requirements: ${error.message}`,
      );
    }
  }

  /**
   * Download email template file
   */
  async downloadFile(id: string): Promise<{
    stream: NodeJS.ReadableStream;
    contentType: string;
    filename?: string;
  }> {
    try {
      const template = await this.emailTemplateModel.findById(id).lean();
      if (!template) {
        throw new NotFoundException('Email template not found');
      }

      if (!template.file) {
        throw new BadRequestException('No file associated with this template');
      }

      const downloadResult = await this.azureStorageService.downloadFile(
        template.file,
      );

      // Extract filename from URL or use template name
      const urlParts = template.file.split('/');
      const filename =
        urlParts[urlParts.length - 1] || `${template.name}-template`;

      return {
        ...downloadResult,
        filename,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to download file: ${error.message}`,
      );
    }
  }
}
