import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  MinLength,
  IsNumber,
  Min,
} from 'class-validator';

export class CreateWebhookDTO {
  @ApiProperty({
    description: 'Webhook URL',
    example: 'https://api.example.com/webhook',
  })
  @IsString()
  @IsNotEmpty()
  webhookUrl: string;

  @ApiProperty({
    description: 'Webhook name',
    example: 'Campaign Notification Webhook',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3, { message: 'Name must be at least 3 characters long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Webhook description',
    example: 'Webhook for sending campaign completion notifications',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;
}

export class UpdateWebhookDTO extends PartialType(CreateWebhookDTO) {}

export class GetWebhookQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Number of records to return',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Search by webhook name',
    example: 'Campaign',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Search by webhook URL',
    example: 'api.example.com',
  })
  @IsOptional()
  @IsString()
  webhookUrl?: string;
}

export class WebhookResponseDTO {
  @ApiProperty({
    description: 'Webhook ID',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id?.toString())
  id: string;

  @ApiProperty({
    description: 'Webhook URL',
    example: 'https://api.example.com/webhook',
  })
  @Expose()
  webhookUrl: string;

  @ApiProperty({
    description: 'Webhook name',
    example: 'Campaign Notification Webhook',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Webhook description',
    example: 'Webhook for sending campaign completion notifications',
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Creator username',
    example: 'john_doe',
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Last update date',
    example: '2024-01-02T00:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;

  @ApiPropertyOptional({
    description: 'Last updater ID',
    example: 123456789,
  })
  @Expose()
  updatedBy?: number;
}

export class GetWebhooksResponseDTO {
  @ApiProperty({
    description: 'List of webhooks',
    type: [WebhookResponseDTO],
  })
  @Expose()
  @Type(() => WebhookResponseDTO)
  data: WebhookResponseDTO[];

  @ApiProperty({
    description: 'Total number of webhooks',
    example: 25,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}
